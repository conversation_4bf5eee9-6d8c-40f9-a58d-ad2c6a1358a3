import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * تطبيق إدارة الشحنات الرئيسي
 */
public class ShipERPMain {
    
    public static void main(String[] args) {
        // إعداد الترميز العربي
        System.setProperty("file.encoding", "UTF-8");
        
        // التأكد من تشغيل التطبيق في الخيط الرئيسي لـ Swing
        SwingUtilities.invokeLater(() -> {
            try {
                // إعداد المظهر
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
                
                // إنشاء وإظهار النافذة
                createAndShowGUI();
                
            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null, 
                    "خطأ في تشغيل التطبيق: " + e.getMessage(), 
                    "خطأ", 
                    JOptionPane.ERROR_MESSAGE);
            }
        });
    }
    
    private static void createAndShowGUI() {
        // إنشاء النافذة الرئيسية
        JFrame frame = new JFrame("نظام إدارة الشحنات - Ship ERP System");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(900, 700);
        frame.setLocationRelativeTo(null);
        
        // إنشاء اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(new Color(245, 245, 245));
        
        // إنشاء شريط العنوان
        JPanel headerPanel = createHeaderPanel();
        
        // إنشاء لوحة المحتوى
        JPanel contentPanel = createContentPanel(frame);
        
        // إنشاء شريط الحالة
        JPanel statusPanel = createStatusPanel();
        
        // تجميع العناصر
        mainPanel.add(headerPanel, BorderLayout.NORTH);
        mainPanel.add(contentPanel, BorderLayout.CENTER);
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
        
        frame.add(mainPanel);
        frame.setVisible(true);
        
        // إظهار رسالة ترحيب
        showWelcomeMessage(frame);
    }
    
    private static JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(new Color(41, 128, 185));
        panel.setBorder(BorderFactory.createEmptyBorder(15, 20, 15, 20));
        
        JLabel titleLabel = new JLabel("نظام إدارة الشحنات", JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 28));
        titleLabel.setForeground(Color.WHITE);
        
        JLabel subtitleLabel = new JLabel("Ship ERP Management System", JLabel.CENTER);
        subtitleLabel.setFont(new Font("Arial", Font.PLAIN, 14));
        subtitleLabel.setForeground(new Color(236, 240, 241));
        
        JPanel titlePanel = new JPanel(new BorderLayout());
        titlePanel.setOpaque(false);
        titlePanel.add(titleLabel, BorderLayout.CENTER);
        titlePanel.add(subtitleLabel, BorderLayout.SOUTH);
        
        panel.add(titlePanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    private static JPanel createContentPanel(JFrame parentFrame) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));
        panel.setBackground(new Color(245, 245, 245));
        
        // لوحة الأزرار الرئيسية
        JPanel buttonPanel = new JPanel(new GridLayout(2, 2, 25, 25));
        buttonPanel.setOpaque(false);
        
        // إنشاء الأزرار
        JButton newShipmentBtn = createMainButton("شحنة جديدة", "إنشاء شحنة جديدة", new Color(52, 152, 219));
        JButton trackBtn = createMainButton("تتبع الشحنات", "تتبع حالة الشحنات", new Color(46, 204, 113));
        JButton customersBtn = createMainButton("إدارة العملاء", "إدارة بيانات العملاء", new Color(231, 76, 60));
        JButton reportsBtn = createMainButton("التقارير", "عرض التقارير والإحصائيات", new Color(243, 156, 18));
        
        // إضافة الأحداث
        newShipmentBtn.addActionListener(e -> showFeatureDialog(parentFrame, "شحنة جديدة", "سيتم فتح نافذة إنشاء شحنة جديدة"));
        trackBtn.addActionListener(e -> showFeatureDialog(parentFrame, "تتبع الشحنات", "سيتم فتح نافذة تتبع الشحنات"));
        customersBtn.addActionListener(e -> showFeatureDialog(parentFrame, "إدارة العملاء", "سيتم فتح نافذة إدارة العملاء"));
        reportsBtn.addActionListener(e -> showFeatureDialog(parentFrame, "التقارير", "سيتم فتح نافذة التقارير والإحصائيات"));
        
        buttonPanel.add(newShipmentBtn);
        buttonPanel.add(trackBtn);
        buttonPanel.add(customersBtn);
        buttonPanel.add(reportsBtn);
        
        // لوحة الإحصائيات
        JPanel statsPanel = createStatsPanel();
        
        panel.add(buttonPanel, BorderLayout.CENTER);
        panel.add(statsPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private static JButton createMainButton(String title, String description, Color color) {
        JButton button = new JButton();
        button.setLayout(new BorderLayout());
        button.setPreferredSize(new Dimension(200, 120));
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        JLabel titleLabel = new JLabel(title, JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 18));
        titleLabel.setForeground(Color.WHITE);
        
        JLabel descLabel = new JLabel(description, JLabel.CENTER);
        descLabel.setFont(new Font("Arial", Font.PLAIN, 12));
        descLabel.setForeground(new Color(255, 255, 255, 200));
        
        JPanel textPanel = new JPanel(new BorderLayout());
        textPanel.setOpaque(false);
        textPanel.add(titleLabel, BorderLayout.CENTER);
        textPanel.add(descLabel, BorderLayout.SOUTH);
        
        button.add(textPanel, BorderLayout.CENTER);
        
        // تأثير hover
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(color.darker());
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(color);
            }
        });
        
        return button;
    }
    
    private static JPanel createStatsPanel() {
        JPanel panel = new JPanel(new GridLayout(1, 4, 15, 15));
        panel.setBorder(BorderFactory.createEmptyBorder(30, 0, 0, 0));
        panel.setOpaque(false);
        
        panel.add(createStatCard("شحنات اليوم", "25", new Color(52, 152, 219)));
        panel.add(createStatCard("معلقة", "8", new Color(231, 76, 60)));
        panel.add(createStatCard("مسلمة", "17", new Color(46, 204, 113)));
        panel.add(createStatCard("إجمالي العملاء", "156", new Color(155, 89, 182)));
        
        return panel;
    }
    
    private static JPanel createStatCard(String title, String value, Color color) {
        JPanel card = new JPanel(new BorderLayout());
        card.setBackground(Color.WHITE);
        card.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(220, 220, 220), 1),
            BorderFactory.createEmptyBorder(15, 15, 15, 15)
        ));
        
        JLabel titleLabel = new JLabel(title, JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.PLAIN, 14));
        titleLabel.setForeground(new Color(100, 100, 100));
        
        JLabel valueLabel = new JLabel(value, JLabel.CENTER);
        valueLabel.setFont(new Font("Arial", Font.BOLD, 24));
        valueLabel.setForeground(color);
        
        card.add(titleLabel, BorderLayout.NORTH);
        card.add(valueLabel, BorderLayout.CENTER);
        
        return card;
    }
    
    private static JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(new Color(236, 240, 241));
        panel.setBorder(BorderFactory.createEmptyBorder(8, 15, 8, 15));
        
        JLabel statusLabel = new JLabel("النظام جاهز للاستخدام");
        statusLabel.setFont(new Font("Arial", Font.PLAIN, 12));
        
        JLabel timeLabel = new JLabel("الوقت: " + java.time.LocalDateTime.now().format(
            java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        timeLabel.setFont(new Font("Arial", Font.PLAIN, 12));
        
        JLabel userLabel = new JLabel("المستخدم: مدير النظام");
        userLabel.setFont(new Font("Arial", Font.PLAIN, 12));
        
        panel.add(statusLabel, BorderLayout.WEST);
        panel.add(userLabel, BorderLayout.CENTER);
        panel.add(timeLabel, BorderLayout.EAST);
        
        return panel;
    }
    
    private static void showWelcomeMessage(JFrame parent) {
        SwingUtilities.invokeLater(() -> {
            JOptionPane.showMessageDialog(parent,
                "مرحباً بك في نظام إدارة الشحنات المتطور!\n\n" +
                "✓ النظام جاهز للاستخدام\n" +
                "✓ جميع الوحدات متاحة\n" +
                "✓ قاعدة البيانات متصلة\n\n" +
                "يمكنك البدء بإنشاء شحنة جديدة أو تتبع الشحنات الموجودة.",
                "مرحباً - نظام إدارة الشحنات",
                JOptionPane.INFORMATION_MESSAGE);
        });
    }
    
    private static void showFeatureDialog(JFrame parent, String feature, String message) {
        JOptionPane.showMessageDialog(parent,
            message + "\n\nهذه الميزة ستكون متاحة في الإصدار القادم.",
            feature,
            JOptionPane.INFORMATION_MESSAGE);
    }
}
