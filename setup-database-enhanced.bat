@echo off
chcp 65001 > nul
echo ========================================
echo    إعداد قاعدة بيانات نظام الشحنات المحسن
echo    Enhanced Ship ERP Database Setup
echo ========================================
echo.

REM إعداد متغيرات البيئة
set SCRIPT_DIR=%~dp0
set LOG_FILE=%SCRIPT_DIR%database-setup.log
set ERROR_FILE=%SCRIPT_DIR%database-setup-error.log

echo [INFO] بدء إعداد قاعدة البيانات...
echo [INFO] مجلد السكريبت: %SCRIPT_DIR%
echo [INFO] ملف السجل: %LOG_FILE%
echo.

REM التحقق من وجود Oracle SQL*Plus
echo [INFO] التحقق من تثبيت Oracle...
sqlplus -v > nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Oracle SQL*Plus غير مثبت أو غير موجود في PATH
    echo [INFO] يرجى تثبيت Oracle Database أو Oracle Client
    echo [INFO] وإضافة مجلد bin إلى متغير PATH
    echo.
    echo [INFO] للتحميل:
    echo   - Oracle Database: https://www.oracle.com/database/
    echo   - Oracle Instant Client: https://www.oracle.com/database/technologies/instant-client.html
    pause
    exit /b 1
)

echo [INFO] تم العثور على Oracle SQL*Plus ✓
echo.

REM التحقق من وجود ملفات السكريبت
if not exist "scripts\setup-database-enhanced.sql" (
    echo [ERROR] ملف السكريبت المحسن غير موجود: scripts\setup-database-enhanced.sql
    if exist "scripts\setup-database.sql" (
        echo [INFO] تم العثور على السكريبت الأساسي، سيتم استخدامه
        set SQL_SCRIPT=scripts\setup-database.sql
    ) else (
        echo [ERROR] لا توجد ملفات سكريبت متاحة
        echo [INFO] تأكد من وجود الملفات في مجلد scripts
        pause
        exit /b 1
    )
) else (
    echo [INFO] تم العثور على السكريبت المحسن ✓
    set SQL_SCRIPT=scripts\setup-database-enhanced.sql
)

echo.

REM طلب معلومات الاتصال مع قيم افتراضية محسنة
echo [INFO] يرجى إدخال معلومات الاتصال بقاعدة البيانات:
echo [INFO] (اضغط Enter لاستخدام القيم الافتراضية)
echo.

set /p DB_HOST="عنوان الخادم [localhost]: "
if "%DB_HOST%"=="" set DB_HOST=localhost

set /p DB_PORT="رقم المنفذ [1521]: "
if "%DB_PORT%"=="" set DB_PORT=1521

set /p DB_SID="اسم قاعدة البيانات [orcl]: "
if "%DB_SID%"=="" set DB_SID=orcl

set /p DBA_USER="مستخدم DBA [system]: "
if "%DBA_USER%"=="" set DBA_USER=system

echo.
echo [INFO] معلومات الاتصال:
echo   الخادم: %DB_HOST%
echo   المنفذ: %DB_PORT%
echo   قاعدة البيانات: %DB_SID%
echo   مستخدم DBA: %DBA_USER%
echo   السكريبت: %SQL_SCRIPT%
echo.

REM عرض معلومات المستخدم الجديد
echo [INFO] سيتم إنشاء مستخدم جديد بالمعلومات التالية:
echo   اسم المستخدم: ship_erp
echo   كلمة المرور: ship_erp_password
echo   Tablespace: SHIP_ERP_DATA (500MB)
echo   Index Tablespace: SHIP_ERP_INDEX (200MB)
echo.

echo [WARN] تحذير: إذا كان المستخدم ship_erp موجوداً، سيتم حذفه وإعادة إنشاؤه!
echo [WARN] سيتم إنشاء Tablespaces جديدة إذا لم تكن موجودة
echo.

set /p CONFIRM="هل تريد المتابعة؟ [y/N]: "
if /i not "%CONFIRM%"=="y" (
    echo [INFO] تم إلغاء العملية
    pause
    exit /b 0
)

echo.
echo [INFO] بدء تشغيل سكريبت إعداد قاعدة البيانات...
echo [INFO] قد تستغرق هذه العملية عدة دقائق...
echo [INFO] يتم حفظ السجل في: %LOG_FILE%
echo.

REM إنشاء ملف السجل
echo ======================================== > "%LOG_FILE%"
echo Ship ERP Database Setup Log >> "%LOG_FILE%"
echo Date: %date% %time% >> "%LOG_FILE%"
echo Host: %DB_HOST%:%DB_PORT%/%DB_SID% >> "%LOG_FILE%"
echo DBA User: %DBA_USER% >> "%LOG_FILE%"
echo Script: %SQL_SCRIPT% >> "%LOG_FILE%"
echo ======================================== >> "%LOG_FILE%"
echo. >> "%LOG_FILE%"

REM تشغيل السكريبت مع تسجيل النتائج
echo [INFO] تشغيل السكريبت...
sqlplus %DBA_USER%@%DB_HOST%:%DB_PORT%/%DB_SID% @%SQL_SCRIPT% >> "%LOG_FILE%" 2>> "%ERROR_FILE%"

set SETUP_RESULT=%errorlevel%

echo.
if %SETUP_RESULT% equ 0 (
    echo [SUCCESS] ✓ تم إعداد قاعدة البيانات بنجاح!
    echo.
    echo [INFO] معلومات الاتصال الجديدة:
    echo   المستخدم: ship_erp
    echo   كلمة المرور: ship_erp_password
    echo   قاعدة البيانات: %DB_HOST%:%DB_PORT%/%DB_SID%
    echo   Tablespace: SHIP_ERP_DATA
    echo   Index Tablespace: SHIP_ERP_INDEX
    echo.
    echo [INFO] الجداول المنشأة:
    echo   ✓ roles (الأدوار)
    echo   ✓ users (المستخدمين)
    echo   ✓ branches (الفروع)
    echo   ✓ currencies (العملات)
    echo   ✓ customers (العملاء)
    echo   ✓ shipments (الشحنات)
    echo   ✓ shipment_tracking (تتبع الشحنات)
    echo   ✓ audit_logs (سجل التدقيق)
    echo   ✓ system_settings (إعدادات النظام)
    echo.
    echo [INFO] البيانات الأساسية:
    echo   ✓ 5 أدوار افتراضية
    echo   ✓ 4 عملات (ريال، دولار، يورو، درهم)
    echo   ✓ 3 فروع (الرياض، جدة، الدمام)
    echo   ✓ 2 مستخدمين (admin, manager)
    echo   ✓ 2 عملاء تجريبيين
    echo   ✓ 9 إعدادات أساسية
    echo.
    echo [INFO] يمكنك الآن:
    echo   1. تشغيل التطبيق باستخدام: java -jar target/ship-erp-system-1.0.0.jar
    echo   2. تسجيل الدخول باستخدام: admin / admin123
    echo   3. الاتصال بقاعدة البيانات باستخدام: ship_erp / ship_erp_password
    echo.
    
    REM إنشاء ملف إعدادات الاتصال
    echo [INFO] إنشاء ملف إعدادات الاتصال...
    echo # Ship ERP Database Connection Settings > database-config.properties
    echo # Generated on %date% %time% >> database-config.properties
    echo. >> database-config.properties
    echo db.host=%DB_HOST% >> database-config.properties
    echo db.port=%DB_PORT% >> database-config.properties
    echo db.sid=%DB_SID% >> database-config.properties
    echo db.username=ship_erp >> database-config.properties
    echo db.password=ship_erp_password >> database-config.properties
    echo db.url=*********************************************% >> database-config.properties
    echo.
    echo [INFO] تم إنشاء ملف الإعدادات: database-config.properties
    
) else (
    echo [ERROR] ✗ فشل في إعداد قاعدة البيانات
    echo.
    echo [INFO] تحقق من الملفات التالية للحصول على تفاصيل الخطأ:
    echo   - ملف السجل: %LOG_FILE%
    echo   - ملف الأخطاء: %ERROR_FILE%
    echo.
    echo [INFO] الأسباب المحتملة للفشل:
    echo   ✗ معلومات اتصال خاطئة
    echo   ✗ عدم وجود صلاحيات DBA للمستخدم
    echo   ✗ خدمة Oracle Database غير متاحة
    echo   ✗ مساحة قرص غير كافية
    echo   ✗ Tablespace موجود ومستخدم
    echo.
    echo [INFO] للمساعدة:
    echo   1. تحقق من تشغيل خدمة Oracle Database
    echo   2. تأكد من صحة معلومات الاتصال
    echo   3. تأكد من وجود صلاحيات DBA
    echo   4. راجع ملفات السجل للحصول على تفاصيل أكثر
)

echo.
echo [INFO] انتهى إعداد قاعدة البيانات
echo [INFO] وقت الانتهاء: %date% %time%
echo.
pause
