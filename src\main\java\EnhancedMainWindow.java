import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import javax.swing.border.EmptyBorder;

/**
 * النافذة الرئيسية المحسنة مع القائمة الشجرية
 * Enhanced Main Window with Tree Menu
 */
public class EnhancedMainWindow extends JFrame {
    
    private Font arabicFont;
    private TreeMenuPanel treeMenuPanel;
    private JPanel contentPanel;
    private JTextField searchField;
    private JLabel statusLabel;
    private JLabel timeLabel;
    private Timer timeTimer;
    private JSplitPane splitPane;
    private boolean menuVisible = true;
    private boolean isFullScreen = true;
    
    public EnhancedMainWindow() {
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        initializeComponents();
        setupLayout();
        setupMenuBar();
        setupStatusBar();
        setupEventHandlers();
        startTimeUpdater();
        
        setTitle("نظام إدارة الشحنات المتقدم - الواجهة الرئيسية");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

        // تحميل حالة ملء الشاشة المحفوظة
        boolean savedFullScreen = loadFullScreenState();
        if (savedFullScreen) {
            setExtendedState(JFrame.MAXIMIZED_BOTH);
            isFullScreen = true;
        } else {
            // تعيين حجم مناسب
            Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
            int width = (int) (screenSize.width * 0.85);
            int height = (int) (screenSize.height * 0.80);
            setSize(width, height);
            isFullScreen = false;
        }
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // تعيين الحد الأدنى للحجم
        setMinimumSize(new Dimension(1000, 600));
        
        // تطبيق المظهر المحفوظ
        SettingsManager.applyStoredTheme();
        SwingUtilities.updateComponentTreeUI(this);
    }
    
    /**
     * تهيئة المكونات
     */
    private void initializeComponents() {
        // القائمة الشجرية
        treeMenuPanel = new TreeMenuPanel(this);
        
        // لوحة المحتوى الرئيسي
        contentPanel = new JPanel(new BorderLayout());
        contentPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        contentPanel.setBackground(Color.WHITE);
        contentPanel.setBorder(new EmptyBorder(20, 20, 20, 20));
        
        // إضافة محتوى ترحيبي
        createWelcomeContent();
        
        // حقل البحث
        searchField = new JTextField();
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.setPreferredSize(new Dimension(200, 30));
        
        // تسميات الحالة
        statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);
        statusLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        timeLabel = new JLabel();
        timeLabel.setFont(arabicFont);
        timeLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        updateTime();
    }
    
    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // لوحة علوية مع البحث
        JPanel topPanel = createTopPanel();
        add(topPanel, BorderLayout.NORTH);
        
        // لوحة رئيسية مقسمة
        splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        splitPane.setRightComponent(treeMenuPanel); // القائمة على اليمين
        splitPane.setLeftComponent(contentPanel);   // المحتوى على اليسار
        // تحميل موضع القائمة المحفوظ أو استخدام القيمة الافتراضية (420 بكسل)
        int savedDividerLocation = loadDividerLocation();
        splitPane.setDividerLocation(savedDividerLocation);
        splitPane.setResizeWeight(0.0); // القائمة ثابتة الحجم
        splitPane.setOneTouchExpandable(true); // إضافة أزرار توسيع/طي سريع
        splitPane.setContinuousLayout(true); // تحديث مستمر أثناء السحب

        // حفظ موضع القائمة عند التغيير
        splitPane.addPropertyChangeListener(JSplitPane.DIVIDER_LOCATION_PROPERTY, e -> {
            if (menuVisible) {
                saveDividerLocation(splitPane.getDividerLocation());
            }
        });
        
        add(splitPane, BorderLayout.CENTER);
    }
    
    /**
     * إنشاء اللوحة العلوية
     */
    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(248, 249, 250));
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createMatteBorder(0, 0, 1, 0, new Color(222, 226, 230)),
            new EmptyBorder(10, 15, 10, 15)
        ));
        
        // عنوان النظام
        JLabel titleLabel = new JLabel("نظام إدارة الشحنات المتقدم");
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 18));
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // لوحة البحث
        JPanel searchPanel = createSearchPanel();
        
        // لوحة أزرار سريعة
        JPanel quickButtonsPanel = createQuickButtonsPanel();
        
        panel.add(titleLabel, BorderLayout.WEST);
        panel.add(searchPanel, BorderLayout.CENTER);
        panel.add(quickButtonsPanel, BorderLayout.EAST);
        
        return panel;
    }
    
    /**
     * إنشاء لوحة البحث
     */
    private JPanel createSearchPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 0));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setOpaque(false);
        
        JLabel searchLabel = new JLabel("البحث في القائمة:");
        searchLabel.setFont(arabicFont);
        searchLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JButton clearSearchBtn = new JButton("مسح");
        clearSearchBtn.setFont(arabicFont);
        clearSearchBtn.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        clearSearchBtn.setPreferredSize(new Dimension(60, 30));
        clearSearchBtn.addActionListener(e -> {
            searchField.setText("");
            treeMenuPanel.expandAllNodes();
        });
        
        panel.add(searchLabel);
        panel.add(searchField);
        panel.add(clearSearchBtn);
        
        return panel;
    }
    
    /**
     * إنشاء لوحة الأزرار السريعة
     */
    private JPanel createQuickButtonsPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 0));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setOpaque(false);
        
        JButton expandBtn = createQuickButton("توسيع الكل", new Color(40, 167, 69));
        expandBtn.addActionListener(e -> treeMenuPanel.expandAllNodes());
        
        JButton collapseBtn = createQuickButton("طي الكل", new Color(220, 53, 69));
        collapseBtn.addActionListener(e -> treeMenuPanel.collapseAllNodes());
        
        JButton refreshBtn = createQuickButton("تحديث", new Color(0, 123, 255));
        refreshBtn.addActionListener(e -> refreshContent());

        JButton toggleMenuBtn = createQuickButton("إخفاء القائمة", new Color(111, 66, 193));
        toggleMenuBtn.addActionListener(e -> toggleMenu(toggleMenuBtn));

        JButton fullScreenBtn = createQuickButton("وضع النافذة", new Color(255, 87, 34));
        fullScreenBtn.addActionListener(e -> toggleFullScreen(fullScreenBtn));

        panel.add(expandBtn);
        panel.add(collapseBtn);
        panel.add(refreshBtn);
        panel.add(toggleMenuBtn);
        panel.add(fullScreenBtn);
        
        return panel;
    }
    
    /**
     * إنشاء زر سريع
     */
    private JButton createQuickButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(new Font("Tahoma", Font.PLAIN, 11));
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setPreferredSize(new Dimension(80, 30));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        
        return button;
    }
    
    /**
     * إنشاء المحتوى الترحيبي
     */
    private void createWelcomeContent() {
        JPanel welcomePanel = new JPanel(new BorderLayout());
        welcomePanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        welcomePanel.setBackground(Color.WHITE);
        
        // رأس ترحيبي
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        headerPanel.setBackground(new Color(52, 152, 219));
        headerPanel.setBorder(new EmptyBorder(30, 30, 30, 30));
        
        JLabel welcomeLabel = new JLabel("مرحباً بك في نظام إدارة الشحنات المتقدم");
        welcomeLabel.setFont(new Font("Tahoma", Font.BOLD, 24));
        welcomeLabel.setForeground(Color.WHITE);
        welcomeLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        welcomeLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        JLabel subtitleLabel = new JLabel("استخدم القائمة الجانبية للوصول إلى جميع وظائف النظام");
        subtitleLabel.setFont(new Font("Tahoma", Font.PLAIN, 16));
        subtitleLabel.setForeground(new Color(236, 240, 241));
        subtitleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        subtitleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        JPanel textPanel = new JPanel(new GridLayout(2, 1, 0, 10));
        textPanel.setOpaque(false);
        textPanel.add(welcomeLabel);
        textPanel.add(subtitleLabel);
        
        headerPanel.add(textPanel, BorderLayout.CENTER);
        
        // محتوى المعلومات
        JPanel infoPanel = createInfoPanel();
        
        welcomePanel.add(headerPanel, BorderLayout.NORTH);
        welcomePanel.add(infoPanel, BorderLayout.CENTER);
        
        contentPanel.add(welcomePanel, BorderLayout.CENTER);
    }
    
    /**
     * إنشاء لوحة المعلومات
     */
    private JPanel createInfoPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 2, 20, 20));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(Color.WHITE);
        panel.setBorder(new EmptyBorder(30, 30, 30, 30));
        
        // بطاقات معلوماتية
        panel.add(createInfoCard("إدارة الشحنات", "إدارة شاملة لجميع عمليات الشحن والتوصيل", new Color(40, 167, 69)));
        panel.add(createInfoCard("إدارة العملاء", "نظام متكامل لإدارة بيانات العملاء وتاريخهم", new Color(255, 193, 7)));
        panel.add(createInfoCard("المحاسبة والمالية", "نظام محاسبي شامل للفواتير والمدفوعات", new Color(220, 53, 69)));
        panel.add(createInfoCard("التقارير والإحصائيات", "تقارير مفصلة وإحصائيات دقيقة للأعمال", new Color(23, 162, 184)));
        
        return panel;
    }
    
    /**
     * إنشاء بطاقة معلوماتية
     */
    private JPanel createInfoCard(String title, String description, Color color) {
        JPanel card = new JPanel(new BorderLayout());
        card.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        card.setBackground(Color.WHITE);
        card.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(color, 2),
            new EmptyBorder(20, 20, 20, 20)
        ));
        
        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
        titleLabel.setForeground(color);
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JTextArea descArea = new JTextArea(description);
        descArea.setFont(arabicFont);
        descArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        descArea.setEditable(false);
        descArea.setOpaque(false);
        descArea.setLineWrap(true);
        descArea.setWrapStyleWord(true);
        
        card.add(titleLabel, BorderLayout.NORTH);
        card.add(descArea, BorderLayout.CENTER);
        
        return card;
    }

    /**
     * إعداد شريط القوائم
     */
    private void setupMenuBar() {
        JMenuBar menuBar = new JMenuBar();
        menuBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // قائمة ملف
        JMenu fileMenu = new JMenu("ملف");
        fileMenu.setFont(arabicFont);
        fileMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JMenuItem newItem = new JMenuItem("جديد");
        newItem.setFont(arabicFont);
        newItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        newItem.setAccelerator(KeyStroke.getKeyStroke(KeyEvent.VK_N, InputEvent.CTRL_DOWN_MASK));

        JMenuItem openItem = new JMenuItem("فتح");
        openItem.setFont(arabicFont);
        openItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        openItem.setAccelerator(KeyStroke.getKeyStroke(KeyEvent.VK_O, InputEvent.CTRL_DOWN_MASK));

        JMenuItem exitItem = new JMenuItem("خروج");
        exitItem.setFont(arabicFont);
        exitItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        exitItem.addActionListener(e -> System.exit(0));

        fileMenu.add(newItem);
        fileMenu.add(openItem);
        fileMenu.addSeparator();
        fileMenu.add(exitItem);

        // قائمة عرض
        JMenu viewMenu = new JMenu("عرض");
        viewMenu.setFont(arabicFont);
        viewMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JMenuItem expandAllItem = new JMenuItem("توسيع جميع القوائم");
        expandAllItem.setFont(arabicFont);
        expandAllItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        expandAllItem.addActionListener(e -> treeMenuPanel.expandAllNodes());

        JMenuItem collapseAllItem = new JMenuItem("طي جميع القوائم");
        collapseAllItem.setFont(arabicFont);
        collapseAllItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        collapseAllItem.addActionListener(e -> treeMenuPanel.collapseAllNodes());

        JMenuItem refreshItem = new JMenuItem("تحديث");
        refreshItem.setFont(arabicFont);
        refreshItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        refreshItem.setAccelerator(KeyStroke.getKeyStroke(KeyEvent.VK_F5, 0));
        refreshItem.addActionListener(e -> refreshContent());

        JMenuItem toggleMenuItemMenu = new JMenuItem("إخفاء/إظهار القائمة الجانبية");
        toggleMenuItemMenu.setFont(arabicFont);
        toggleMenuItemMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        toggleMenuItemMenu.setAccelerator(KeyStroke.getKeyStroke(KeyEvent.VK_F9, 0));
        toggleMenuItemMenu.addActionListener(e -> {
            // البحث عن زر التبديل وتنفيذ العملية
            Component[] components = ((JPanel) getContentPane().getComponent(0)).getComponents();
            for (Component comp : components) {
                if (comp instanceof JPanel) {
                    JPanel panel = (JPanel) comp;
                    for (Component subComp : panel.getComponents()) {
                        if (subComp instanceof JButton && ((JButton) subComp).getText().contains("القائمة")) {
                            toggleMenu((JButton) subComp);
                            return;
                        }
                    }
                }
            }
        });

        JMenuItem fullScreenMenuItem = new JMenuItem("التبديل إلى الوضع العادي");
        fullScreenMenuItem.setFont(arabicFont);
        fullScreenMenuItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        fullScreenMenuItem.setAccelerator(KeyStroke.getKeyStroke(KeyEvent.VK_F11, 0));
        fullScreenMenuItem.addActionListener(e -> {
            // البحث عن زر ملء الشاشة وتنفيذ العملية
            Component[] components = ((JPanel) getContentPane().getComponent(0)).getComponents();
            for (Component comp : components) {
                if (comp instanceof JPanel) {
                    JPanel panel = (JPanel) comp;
                    for (Component subComp : panel.getComponents()) {
                        if (subComp instanceof JButton &&
                            (((JButton) subComp).getText().contains("ملء الشاشة") ||
                             ((JButton) subComp).getText().contains("وضع النافذة"))) {
                            toggleFullScreen((JButton) subComp);
                            // تحديث نص العنصر في القائمة
                            if (isFullScreen) {
                                fullScreenMenuItem.setText("التبديل إلى الوضع العادي");
                            } else {
                                fullScreenMenuItem.setText("التبديل إلى ملء الشاشة");
                            }
                            return;
                        }
                    }
                }
            }
        });

        viewMenu.add(expandAllItem);
        viewMenu.add(collapseAllItem);
        viewMenu.addSeparator();
        viewMenu.add(refreshItem);
        viewMenu.addSeparator();
        viewMenu.add(toggleMenuItemMenu);
        viewMenu.add(fullScreenMenuItem);

        // قائمة أدوات
        JMenu toolsMenu = new JMenu("أدوات");
        toolsMenu.setFont(arabicFont);
        toolsMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JMenuItem settingsItem = new JMenuItem("الإعدادات العامة");
        settingsItem.setFont(arabicFont);
        settingsItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        settingsItem.addActionListener(e -> {
            GeneralSettingsWindow settingsWindow = new GeneralSettingsWindow(this);
            settingsWindow.setVisible(true);
        });

        JMenuItem usersItem = new JMenuItem("إدارة المستخدمين");
        usersItem.setFont(arabicFont);
        usersItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        usersItem.addActionListener(e -> {
            UserManagementWindow usersWindow = new UserManagementWindow(this);
            usersWindow.setVisible(true);
        });

        toolsMenu.add(settingsItem);
        toolsMenu.add(usersItem);

        // قائمة مساعدة
        JMenu helpMenu = new JMenu("مساعدة");
        helpMenu.setFont(arabicFont);
        helpMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JMenuItem aboutItem = new JMenuItem("حول البرنامج");
        aboutItem.setFont(arabicFont);
        aboutItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        aboutItem.addActionListener(e -> showAboutDialog());

        helpMenu.add(aboutItem);

        menuBar.add(fileMenu);
        menuBar.add(viewMenu);
        menuBar.add(toolsMenu);
        menuBar.add(helpMenu);

        setJMenuBar(menuBar);
    }

    /**
     * إعداد شريط الحالة
     */
    private void setupStatusBar() {
        JPanel statusBar = new JPanel(new BorderLayout());
        statusBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusBar.setBackground(new Color(248, 249, 250));
        statusBar.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createMatteBorder(1, 0, 0, 0, new Color(222, 226, 230)),
            new EmptyBorder(5, 15, 5, 15)
        ));

        statusBar.add(statusLabel, BorderLayout.WEST);
        statusBar.add(timeLabel, BorderLayout.EAST);

        add(statusBar, BorderLayout.SOUTH);
    }

    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        // البحث في القائمة
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                String searchText = searchField.getText();
                treeMenuPanel.searchInMenu(searchText);

                if (searchText.isEmpty()) {
                    statusLabel.setText("جاهز");
                } else {
                    statusLabel.setText("البحث عن: " + searchText);
                }
            }
        });

        // اختصارات لوحة المفاتيح
        setupKeyboardShortcuts();
    }

    /**
     * إعداد اختصارات لوحة المفاتيح
     */
    private void setupKeyboardShortcuts() {
        // Ctrl+F للبحث
        getRootPane().getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW)
            .put(KeyStroke.getKeyStroke(KeyEvent.VK_F, InputEvent.CTRL_DOWN_MASK), "search");
        getRootPane().getActionMap().put("search", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                searchField.requestFocus();
            }
        });

        // Escape لمسح البحث
        getRootPane().getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW)
            .put(KeyStroke.getKeyStroke(KeyEvent.VK_ESCAPE, 0), "clearSearch");
        getRootPane().getActionMap().put("clearSearch", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                searchField.setText("");
                treeMenuPanel.expandAllNodes();
                statusLabel.setText("جاهز");
            }
        });

        // F11 للتبديل بين ملء الشاشة والوضع العادي
        getRootPane().getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW)
            .put(KeyStroke.getKeyStroke(KeyEvent.VK_F11, 0), "toggleFullScreen");
        getRootPane().getActionMap().put("toggleFullScreen", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // البحث عن زر ملء الشاشة وتنفيذ العملية
                Component[] components = ((JPanel) getContentPane().getComponent(0)).getComponents();
                for (Component comp : components) {
                    if (comp instanceof JPanel) {
                        JPanel panel = (JPanel) comp;
                        for (Component subComp : panel.getComponents()) {
                            if (subComp instanceof JButton &&
                                (((JButton) subComp).getText().contains("ملء الشاشة") ||
                                 ((JButton) subComp).getText().contains("وضع النافذة"))) {
                                toggleFullScreen((JButton) subComp);
                                return;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * بدء مُحدث الوقت
     */
    private void startTimeUpdater() {
        timeTimer = new Timer(1000, e -> updateTime());
        timeTimer.start();
    }

    /**
     * تحديث الوقت
     */
    private void updateTime() {
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        timeLabel.setText(sdf.format(new java.util.Date()));
    }

    /**
     * تحديث المحتوى
     */
    private void refreshContent() {
        statusLabel.setText("جاري التحديث...");

        // محاكاة عملية التحديث
        Timer refreshTimer = new Timer(1000, e -> {
            statusLabel.setText("تم التحديث بنجاح");

            // إعادة تعيين الحالة بعد 3 ثوان
            Timer resetTimer = new Timer(3000, ev -> statusLabel.setText("جاهز"));
            resetTimer.setRepeats(false);
            resetTimer.start();
        });
        refreshTimer.setRepeats(false);
        refreshTimer.start();
    }

    /**
     * عرض نافذة حول البرنامج
     */
    private void showAboutDialog() {
        String aboutText = "نظام إدارة الشحنات المتقدم\n\n" +
                          "الإصدار: 2.0\n" +
                          "تاريخ الإصدار: 2024\n\n" +
                          "الميزات:\n" +
                          "• واجهة شجرية متقدمة\n" +
                          "• دعم كامل للغة العربية\n" +
                          "• نظام بحث متطور\n" +
                          "• إدارة شاملة للمستخدمين\n" +
                          "• تقارير وإحصائيات مفصلة\n\n" +
                          "حقوق الطبع محفوظة © 2024";

        JTextArea textArea = new JTextArea(aboutText);
        textArea.setFont(arabicFont);
        textArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        textArea.setEditable(false);
        textArea.setOpaque(false);

        JOptionPane.showMessageDialog(this, textArea, "حول البرنامج", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * تنظيف الموارد عند الإغلاق
     */
    @Override
    public void dispose() {
        if (timeTimer != null) {
            timeTimer.stop();
        }
        super.dispose();
    }

    /**
     * إخفاء/إظهار القائمة الجانبية
     */
    private void toggleMenu(JButton toggleButton) {
        if (menuVisible) {
            // إخفاء القائمة
            splitPane.setDividerLocation(0);
            splitPane.setRightComponent(null);
            toggleButton.setText("إظهار القائمة");
            statusLabel.setText("تم إخفاء القائمة الجانبية");
            menuVisible = false;
        } else {
            // إظهار القائمة
            splitPane.setRightComponent(treeMenuPanel);
            splitPane.setDividerLocation(420); // تغيير إلى 420 بكسل
            toggleButton.setText("إخفاء القائمة");
            statusLabel.setText("تم إظهار القائمة الجانبية");
            menuVisible = true;
        }

        // إعادة تعيين الحالة بعد 2 ثانية
        Timer resetTimer = new Timer(2000, e -> statusLabel.setText("جاهز"));
        resetTimer.setRepeats(false);
        resetTimer.start();
    }

    /**
     * التبديل بين وضع ملء الشاشة والوضع العادي
     */
    private void toggleFullScreen(JButton fullScreenButton) {
        if (isFullScreen) {
            // التبديل إلى الوضع العادي
            setExtendedState(JFrame.NORMAL);

            // تعيين حجم مناسب
            Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
            int width = (int) (screenSize.width * 0.85);
            int height = (int) (screenSize.height * 0.80);
            setSize(width, height);
            setLocationRelativeTo(null);

            fullScreenButton.setText("ملء الشاشة");
            statusLabel.setText("تم التبديل إلى الوضع العادي");
            isFullScreen = false;
        } else {
            // التبديل إلى وضع ملء الشاشة
            setExtendedState(JFrame.MAXIMIZED_BOTH);

            fullScreenButton.setText("وضع النافذة");
            statusLabel.setText("تم التبديل إلى وضع ملء الشاشة");
            isFullScreen = true;
        }

        // حفظ حالة ملء الشاشة
        saveFullScreenState(isFullScreen);

        // إعادة تعيين الحالة بعد 2 ثانية
        Timer resetTimer = new Timer(2000, e -> statusLabel.setText("جاهز"));
        resetTimer.setRepeats(false);
        resetTimer.start();
    }

    /**
     * تحميل موضع القائمة المحفوظ
     */
    private int loadDividerLocation() {
        try {
            java.util.Properties settings = SettingsManager.loadSettings();
            String savedLocation = settings.getProperty("ui.divider.location", "420");
            return Integer.parseInt(savedLocation);
        } catch (Exception e) {
            return 420; // القيمة الافتراضية الجديدة - 420 بكسل
        }
    }

    /**
     * حفظ موضع القائمة
     */
    private void saveDividerLocation(int location) {
        try {
            java.util.Properties settings = SettingsManager.loadSettings();
            settings.setProperty("ui.divider.location", String.valueOf(location));

            // حفظ الإعدادات
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream("ship_erp_settings.properties")) {
                settings.store(fos, "Ship ERP Settings - إعدادات نظام إدارة الشحنات");
            }
        } catch (Exception e) {
            System.err.println("خطأ في حفظ موضع القائمة: " + e.getMessage());
        }
    }

    /**
     * تحميل حالة ملء الشاشة المحفوظة
     */
    private boolean loadFullScreenState() {
        try {
            java.util.Properties settings = SettingsManager.loadSettings();
            String savedState = settings.getProperty("ui.fullscreen", "true");
            return Boolean.parseBoolean(savedState);
        } catch (Exception e) {
            return true; // القيمة الافتراضية - ملء الشاشة
        }
    }

    /**
     * حفظ حالة ملء الشاشة
     */
    private void saveFullScreenState(boolean fullScreen) {
        try {
            java.util.Properties settings = SettingsManager.loadSettings();
            settings.setProperty("ui.fullscreen", String.valueOf(fullScreen));

            // حفظ الإعدادات
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream("ship_erp_settings.properties")) {
                settings.store(fos, "Ship ERP Settings - إعدادات نظام إدارة الشحنات");
            }
        } catch (Exception e) {
            System.err.println("خطأ في حفظ حالة ملء الشاشة: " + e.getMessage());
        }
    }
}
