import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Stage;
import com.formdev.flatlaf.FlatLightLaf;

import javax.swing.UIManager;

/**
 * نسخة مبسطة من تطبيق إدارة الشحنات
 */
public class SimpleShipERP extends Application {

    @Override
    public void start(Stage primaryStage) {
        // إعداد المظهر
        try {
            UIManager.setLookAndFeel(new FlatLightLaf());
        } catch (Exception e) {
            e.printStackTrace();
        }

        primaryStage.setTitle("نظام إدارة الشحنات - Ship ERP");
        primaryStage.setWidth(1200);
        primaryStage.setHeight(800);

        // إنشاء القائمة الرئيسية
        MenuBar menuBar = createMenuBar();

        // إنشاء المحتوى الرئيسي
        VBox mainContent = createMainContent();

        // إنشاء شريط الحالة
        HBox statusBar = createStatusBar();

        // تجميع العناصر
        BorderPane root = new BorderPane();
        root.setTop(menuBar);
        root.setCenter(mainContent);
        root.setBottom(statusBar);

        Scene scene = new Scene(root);
        scene.getStylesheets().add(getClass().getResource("/css/styles.css").toExternalForm());
        
        primaryStage.setScene(scene);
        primaryStage.show();

        // إظهار رسالة ترحيب
        showWelcomeDialog();
    }

    private MenuBar createMenuBar() {
        MenuBar menuBar = new MenuBar();

        // قائمة الملف
        Menu fileMenu = new Menu("ملف");
        MenuItem newItem = new MenuItem("جديد");
        MenuItem openItem = new MenuItem("فتح");
        MenuItem saveItem = new MenuItem("حفظ");
        MenuItem exitItem = new MenuItem("خروج");
        exitItem.setOnAction(e -> System.exit(0));
        fileMenu.getItems().addAll(newItem, openItem, saveItem, new SeparatorMenuItem(), exitItem);

        // قائمة الشحنات
        Menu shipmentMenu = new Menu("الشحنات");
        MenuItem newShipment = new MenuItem("شحنة جديدة");
        MenuItem trackShipment = new MenuItem("تتبع الشحنات");
        MenuItem shipmentReports = new MenuItem("تقارير الشحنات");
        shipmentMenu.getItems().addAll(newShipment, trackShipment, shipmentReports);

        // قائمة العملاء
        Menu customerMenu = new Menu("العملاء");
        MenuItem newCustomer = new MenuItem("عميل جديد");
        MenuItem customerList = new MenuItem("قائمة العملاء");
        customerMenu.getItems().addAll(newCustomer, customerList);

        // قائمة التقارير
        Menu reportsMenu = new Menu("التقارير");
        MenuItem dailyReport = new MenuItem("تقرير يومي");
        MenuItem monthlyReport = new MenuItem("تقرير شهري");
        reportsMenu.getItems().addAll(dailyReport, monthlyReport);

        // قائمة المساعدة
        Menu helpMenu = new Menu("مساعدة");
        MenuItem aboutItem = new MenuItem("حول البرنامج");
        aboutItem.setOnAction(e -> showAboutDialog());
        helpMenu.getItems().add(aboutItem);

        menuBar.getMenus().addAll(fileMenu, shipmentMenu, customerMenu, reportsMenu, helpMenu);
        return menuBar;
    }

    private VBox createMainContent() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));
        content.setAlignment(Pos.CENTER);

        // عنوان رئيسي
        Label titleLabel = new Label("مرحباً بك في نظام إدارة الشحنات");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // أزرار سريعة
        HBox quickButtons = new HBox(15);
        quickButtons.setAlignment(Pos.CENTER);

        Button newShipmentBtn = new Button("شحنة جديدة");
        newShipmentBtn.setPrefSize(150, 50);
        newShipmentBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-size: 14px;");

        Button trackBtn = new Button("تتبع الشحنات");
        trackBtn.setPrefSize(150, 50);
        trackBtn.setStyle("-fx-background-color: #2ecc71; -fx-text-fill: white; -fx-font-size: 14px;");

        Button customersBtn = new Button("إدارة العملاء");
        customersBtn.setPrefSize(150, 50);
        customersBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-size: 14px;");

        Button reportsBtn = new Button("التقارير");
        reportsBtn.setPrefSize(150, 50);
        reportsBtn.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white; -fx-font-size: 14px;");

        quickButtons.getChildren().addAll(newShipmentBtn, trackBtn, customersBtn, reportsBtn);

        // معلومات سريعة
        GridPane infoGrid = new GridPane();
        infoGrid.setHgap(20);
        infoGrid.setVgap(15);
        infoGrid.setAlignment(Pos.CENTER);

        Label shipmentCountLabel = new Label("عدد الشحنات اليوم:");
        Label shipmentCount = new Label("25");
        shipmentCount.setStyle("-fx-font-weight: bold; -fx-text-fill: #3498db;");

        Label pendingLabel = new Label("الشحنات المعلقة:");
        Label pendingCount = new Label("8");
        pendingCount.setStyle("-fx-font-weight: bold; -fx-text-fill: #e74c3c;");

        Label deliveredLabel = new Label("الشحنات المسلمة:");
        Label deliveredCount = new Label("17");
        deliveredCount.setStyle("-fx-font-weight: bold; -fx-text-fill: #2ecc71;");

        infoGrid.add(shipmentCountLabel, 0, 0);
        infoGrid.add(shipmentCount, 1, 0);
        infoGrid.add(pendingLabel, 0, 1);
        infoGrid.add(pendingCount, 1, 1);
        infoGrid.add(deliveredLabel, 0, 2);
        infoGrid.add(deliveredCount, 1, 2);

        content.getChildren().addAll(titleLabel, quickButtons, infoGrid);
        return content;
    }

    private HBox createStatusBar() {
        HBox statusBar = new HBox();
        statusBar.setPadding(new Insets(5, 10, 5, 10));
        statusBar.setStyle("-fx-background-color: #ecf0f1; -fx-border-color: #bdc3c7; -fx-border-width: 1 0 0 0;");

        Label statusLabel = new Label("جاهز");
        Label userLabel = new Label("المستخدم: مدير النظام");
        Label timeLabel = new Label("الوقت: " + java.time.LocalDateTime.now().format(
            java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));

        Region spacer1 = new Region();
        Region spacer2 = new Region();
        HBox.setHgrow(spacer1, Priority.ALWAYS);
        HBox.setHgrow(spacer2, Priority.ALWAYS);

        statusBar.getChildren().addAll(statusLabel, spacer1, userLabel, spacer2, timeLabel);
        return statusBar;
    }

    private void showWelcomeDialog() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("مرحباً");
        alert.setHeaderText("نظام إدارة الشحنات");
        alert.setContentText("مرحباً بك في نظام إدارة الشحنات المتطور!\n\nالنظام جاهز للاستخدام.");
        alert.showAndWait();
    }

    private void showAboutDialog() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("حول البرنامج");
        alert.setHeaderText("نظام إدارة الشحنات");
        alert.setContentText("الإصدار: 1.0.0\nتطوير: فريق التطوير\nحقوق الطبع محفوظة © 2025");
        alert.showAndWait();
    }

    public static void main(String[] args) {
        launch(args);
    }
}
