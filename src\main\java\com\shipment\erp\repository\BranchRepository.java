package com.shipment.erp.repository;

import com.shipment.erp.model.Branch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * مستودع الفروع
 */
@Repository
public interface BranchRepository extends JpaRepository<Branch, Long> {
    
    /**
     * البحث عن فرع بالكود
     */
    Optional<Branch> findByCode(String code);
    
    /**
     * التحقق من وجود فرع بالكود
     */
    boolean existsByCode(String code);
    
    /**
     * الحصول على الفروع النشطة
     */
    List<Branch> findByActiveTrue();
    
    /**
     * الحصول على الفروع غير النشطة
     */
    List<Branch> findByActiveFalse();
    
    /**
     * البحث عن الفروع حسب المدينة
     */
    List<Branch> findByCity(String city);
    
    /**
     * البحث عن الفروع حسب المنطقة
     */
    List<Branch> findByRegion(String region);
    
    /**
     * البحث عن الفروع بالاسم (يحتوي على)
     */
    List<Branch> findByNameContainingIgnoreCase(String name);
    
    /**
     * الحصول على الفروع مرتبة بالاسم
     */
    List<Branch> findAllByOrderByNameAsc();
    
    /**
     * البحث عن الفروع حسب المدينة والحالة النشطة
     */
    List<Branch> findByCityAndActiveTrue(String city);
}
