-- ========================================
-- إعداد قاعدة بيانات نظام إدارة الشحنات المحسن
-- Enhanced Ship ERP Database Setup Script
-- ========================================

-- تعيين إعدادات الجلسة
SET ECHO ON
SET FEEDBACK ON
SET SERVEROUTPUT ON SIZE 1000000
SET LINESIZE 1000
SET PAGESIZE 0

-- متغيرات النظام
DEFINE app_user = 'ship_erp'
DEFINE app_password = 'ship_erp_password'
DEFINE app_tablespace = 'SHIP_ERP_DATA'
DEFINE app_index_tablespace = 'SHIP_ERP_INDEX'

PROMPT ========================================
PROMPT بدء إعداد قاعدة بيانات نظام الشحنات المحسن
PROMPT Enhanced Ship ERP Database Setup Started
PROMPT ========================================

-- إنشاء Tablespace للبيانات (مع معالجة الأخطاء)
PROMPT إنشاء Tablespace للبيانات...
DECLARE
    tablespace_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO tablespace_exists FROM dba_tablespaces WHERE tablespace_name = '&app_tablespace';
    IF tablespace_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLESPACE &app_tablespace
        DATAFILE ''ship_erp_data.dbf'' SIZE 500M
        AUTOEXTEND ON NEXT 100M MAXSIZE 2G
        EXTENT MANAGEMENT LOCAL
        SEGMENT SPACE MANAGEMENT AUTO';
        DBMS_OUTPUT.PUT_LINE('تم إنشاء Tablespace: &app_tablespace');
    ELSE
        DBMS_OUTPUT.PUT_LINE('Tablespace موجود بالفعل: &app_tablespace');
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('خطأ في إنشاء Tablespace: ' || SQLERRM);
END;
/

-- إنشاء Tablespace للفهارس
PROMPT إنشاء Tablespace للفهارس...
DECLARE
    tablespace_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO tablespace_exists FROM dba_tablespaces WHERE tablespace_name = '&app_index_tablespace';
    IF tablespace_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLESPACE &app_index_tablespace
        DATAFILE ''ship_erp_index.dbf'' SIZE 200M
        AUTOEXTEND ON NEXT 50M MAXSIZE 1G
        EXTENT MANAGEMENT LOCAL
        SEGMENT SPACE MANAGEMENT AUTO';
        DBMS_OUTPUT.PUT_LINE('تم إنشاء Index Tablespace: &app_index_tablespace');
    ELSE
        DBMS_OUTPUT.PUT_LINE('Index Tablespace موجود بالفعل: &app_index_tablespace');
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('خطأ في إنشاء Index Tablespace: ' || SQLERRM);
END;
/

-- حذف المستخدم إذا كان موجوداً
PROMPT التحقق من وجود المستخدم وحذفه إذا لزم الأمر...
DECLARE
    user_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO user_exists FROM dba_users WHERE username = UPPER('&app_user');
    IF user_exists > 0 THEN
        EXECUTE IMMEDIATE 'DROP USER &app_user CASCADE';
        DBMS_OUTPUT.PUT_LINE('تم حذف المستخدم الموجود: &app_user');
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('خطأ في حذف المستخدم: ' || SQLERRM);
END;
/

-- إنشاء المستخدم الجديد
PROMPT إنشاء المستخدم الجديد...
CREATE USER &app_user IDENTIFIED BY &app_password
DEFAULT TABLESPACE &app_tablespace
TEMPORARY TABLESPACE TEMP
QUOTA UNLIMITED ON &app_tablespace
QUOTA UNLIMITED ON &app_index_tablespace;

-- منح الصلاحيات الأساسية
PROMPT منح الصلاحيات للمستخدم...
GRANT CONNECT TO &app_user;
GRANT RESOURCE TO &app_user;
GRANT CREATE SESSION TO &app_user;
GRANT CREATE TABLE TO &app_user;
GRANT CREATE VIEW TO &app_user;
GRANT CREATE SEQUENCE TO &app_user;
GRANT CREATE PROCEDURE TO &app_user;
GRANT CREATE TRIGGER TO &app_user;
GRANT CREATE SYNONYM TO &app_user;
GRANT CREATE TYPE TO &app_user;

-- منح صلاحيات إضافية للتطوير
GRANT CREATE MATERIALIZED VIEW TO &app_user;
GRANT CREATE DATABASE LINK TO &app_user;
GRANT CREATE JOB TO &app_user;

-- الاتصال بالمستخدم الجديد
PROMPT الاتصال بالمستخدم الجديد...
CONNECT &app_user/&app_password

-- إنشاء الجداول الأساسية
PROMPT إنشاء الجداول الأساسية...

-- جدول الأدوار
CREATE TABLE roles (
    id NUMBER(10) CONSTRAINT pk_roles PRIMARY KEY,
    name VARCHAR2(100) NOT NULL CONSTRAINT uk_roles_name UNIQUE,
    description VARCHAR2(500),
    permissions CLOB,
    active NUMBER(1) DEFAULT 1 CONSTRAINT ck_roles_active CHECK (active IN (0,1)),
    created_date DATE DEFAULT SYSDATE,
    created_by VARCHAR2(100),
    modified_date DATE,
    modified_by VARCHAR2(100)
) TABLESPACE &app_tablespace;

-- جدول المستخدمين
CREATE TABLE users (
    id NUMBER(10) CONSTRAINT pk_users PRIMARY KEY,
    username VARCHAR2(50) NOT NULL CONSTRAINT uk_users_username UNIQUE,
    password_hash VARCHAR2(255) NOT NULL,
    email VARCHAR2(100) CONSTRAINT uk_users_email UNIQUE,
    full_name VARCHAR2(200),
    phone VARCHAR2(20),
    mobile VARCHAR2(20),
    role_id NUMBER(10),
    branch_id NUMBER(10),
    active NUMBER(1) DEFAULT 1 CONSTRAINT ck_users_active CHECK (active IN (0,1)),
    last_login DATE,
    login_attempts NUMBER(2) DEFAULT 0,
    locked_until DATE,
    password_changed_date DATE DEFAULT SYSDATE,
    created_date DATE DEFAULT SYSDATE,
    created_by VARCHAR2(100),
    modified_date DATE,
    modified_by VARCHAR2(100)
) TABLESPACE &app_tablespace;

-- جدول الفروع
CREATE TABLE branches (
    id NUMBER(10) CONSTRAINT pk_branches PRIMARY KEY,
    code VARCHAR2(20) NOT NULL CONSTRAINT uk_branches_code UNIQUE,
    name VARCHAR2(200) NOT NULL,
    address VARCHAR2(500),
    city VARCHAR2(100),
    region VARCHAR2(100),
    country VARCHAR2(100) DEFAULT 'Saudi Arabia',
    postal_code VARCHAR2(20),
    phone VARCHAR2(20),
    fax VARCHAR2(20),
    email VARCHAR2(100),
    manager_name VARCHAR2(200),
    manager_phone VARCHAR2(20),
    coordinates VARCHAR2(100),
    working_hours VARCHAR2(200),
    active NUMBER(1) DEFAULT 1 CONSTRAINT ck_branches_active CHECK (active IN (0,1)),
    created_date DATE DEFAULT SYSDATE,
    created_by VARCHAR2(100),
    modified_date DATE,
    modified_by VARCHAR2(100)
) TABLESPACE &app_tablespace;

-- جدول العملات
CREATE TABLE currencies (
    id NUMBER(10) CONSTRAINT pk_currencies PRIMARY KEY,
    code VARCHAR2(10) NOT NULL CONSTRAINT uk_currencies_code UNIQUE,
    name VARCHAR2(100) NOT NULL,
    symbol VARCHAR2(10),
    exchange_rate NUMBER(10,4) DEFAULT 1,
    is_default NUMBER(1) DEFAULT 0 CONSTRAINT ck_currencies_default CHECK (is_default IN (0,1)),
    active NUMBER(1) DEFAULT 1 CONSTRAINT ck_currencies_active CHECK (active IN (0,1)),
    last_updated DATE DEFAULT SYSDATE,
    created_date DATE DEFAULT SYSDATE,
    created_by VARCHAR2(100),
    modified_date DATE,
    modified_by VARCHAR2(100)
) TABLESPACE &app_tablespace;

-- جدول العملاء
CREATE TABLE customers (
    id NUMBER(10) CONSTRAINT pk_customers PRIMARY KEY,
    code VARCHAR2(20) NOT NULL CONSTRAINT uk_customers_code UNIQUE,
    name VARCHAR2(200) NOT NULL,
    company_name VARCHAR2(200),
    customer_type VARCHAR2(50) DEFAULT 'INDIVIDUAL' CONSTRAINT ck_customers_type CHECK (customer_type IN ('INDIVIDUAL', 'COMPANY')),
    email VARCHAR2(100),
    phone VARCHAR2(20),
    mobile VARCHAR2(20),
    fax VARCHAR2(20),
    address VARCHAR2(500),
    city VARCHAR2(100),
    region VARCHAR2(100),
    country VARCHAR2(100) DEFAULT 'Saudi Arabia',
    postal_code VARCHAR2(20),
    tax_number VARCHAR2(50),
    credit_limit NUMBER(12,2) DEFAULT 0,
    current_balance NUMBER(12,2) DEFAULT 0,
    preferred_currency_id NUMBER(10),
    notes CLOB,
    active NUMBER(1) DEFAULT 1 CONSTRAINT ck_customers_active CHECK (active IN (0,1)),
    created_date DATE DEFAULT SYSDATE,
    created_by VARCHAR2(100),
    modified_date DATE,
    modified_by VARCHAR2(100)
) TABLESPACE &app_tablespace;

-- إضافة المراجع الخارجية
ALTER TABLE users ADD CONSTRAINT fk_users_role FOREIGN KEY (role_id) REFERENCES roles(id);
ALTER TABLE users ADD CONSTRAINT fk_users_branch FOREIGN KEY (branch_id) REFERENCES branches(id);
ALTER TABLE customers ADD CONSTRAINT fk_customers_currency FOREIGN KEY (preferred_currency_id) REFERENCES currencies(id);

-- جدول الشحنات
CREATE TABLE shipments (
    id NUMBER(10) CONSTRAINT pk_shipments PRIMARY KEY,
    tracking_number VARCHAR2(50) NOT NULL CONSTRAINT uk_shipments_tracking UNIQUE,
    customer_id NUMBER(10) NOT NULL,
    sender_name VARCHAR2(200),
    sender_phone VARCHAR2(20),
    sender_address VARCHAR2(500),
    sender_city VARCHAR2(100),
    receiver_name VARCHAR2(200),
    receiver_phone VARCHAR2(20),
    receiver_address VARCHAR2(500),
    receiver_city VARCHAR2(100),
    origin_branch_id NUMBER(10),
    destination_branch_id NUMBER(10),
    service_type VARCHAR2(50) DEFAULT 'STANDARD' CONSTRAINT ck_shipments_service CHECK (service_type IN ('EXPRESS', 'STANDARD', 'ECONOMY')),
    package_type VARCHAR2(50) DEFAULT 'PACKAGE',
    weight NUMBER(8,2),
    dimensions VARCHAR2(100),
    pieces NUMBER(5) DEFAULT 1,
    declared_value NUMBER(12,2),
    shipping_cost NUMBER(12,2),
    insurance_cost NUMBER(12,2) DEFAULT 0,
    total_cost NUMBER(12,2),
    currency_id NUMBER(10),
    payment_method VARCHAR2(50) DEFAULT 'CASH',
    payment_status VARCHAR2(50) DEFAULT 'PENDING',
    status VARCHAR2(50) DEFAULT 'PENDING' CONSTRAINT ck_shipments_status CHECK (status IN ('PENDING', 'PICKED_UP', 'IN_TRANSIT', 'OUT_FOR_DELIVERY', 'DELIVERED', 'RETURNED', 'CANCELLED')),
    priority VARCHAR2(20) DEFAULT 'NORMAL',
    special_instructions CLOB,
    notes CLOB,
    pickup_date DATE,
    expected_delivery_date DATE,
    actual_delivery_date DATE,
    delivered_to VARCHAR2(200),
    delivery_signature BLOB,
    cod_amount NUMBER(12,2) DEFAULT 0,
    created_date DATE DEFAULT SYSDATE,
    created_by VARCHAR2(100),
    modified_date DATE,
    modified_by VARCHAR2(100),
    CONSTRAINT fk_shipments_customer FOREIGN KEY (customer_id) REFERENCES customers(id),
    CONSTRAINT fk_shipments_origin FOREIGN KEY (origin_branch_id) REFERENCES branches(id),
    CONSTRAINT fk_shipments_destination FOREIGN KEY (destination_branch_id) REFERENCES branches(id),
    CONSTRAINT fk_shipments_currency FOREIGN KEY (currency_id) REFERENCES currencies(id)
) TABLESPACE &app_tablespace;

-- جدول تتبع الشحنات
CREATE TABLE shipment_tracking (
    id NUMBER(10) CONSTRAINT pk_shipment_tracking PRIMARY KEY,
    shipment_id NUMBER(10) NOT NULL,
    status VARCHAR2(50) NOT NULL,
    location VARCHAR2(200),
    branch_id NUMBER(10),
    latitude NUMBER(10,6),
    longitude NUMBER(10,6),
    notes VARCHAR2(500),
    timestamp DATE DEFAULT SYSDATE,
    created_by VARCHAR2(100),
    CONSTRAINT fk_tracking_shipment FOREIGN KEY (shipment_id) REFERENCES shipments(id),
    CONSTRAINT fk_tracking_branch FOREIGN KEY (branch_id) REFERENCES branches(id)
) TABLESPACE &app_tablespace;

-- جدول سجل التدقيق
CREATE TABLE audit_logs (
    id NUMBER(10) CONSTRAINT pk_audit_logs PRIMARY KEY,
    user_id NUMBER(10),
    username VARCHAR2(100),
    action VARCHAR2(100) NOT NULL,
    table_name VARCHAR2(100),
    record_id NUMBER(10),
    old_values CLOB,
    new_values CLOB,
    ip_address VARCHAR2(50),
    user_agent VARCHAR2(500),
    session_id VARCHAR2(100),
    timestamp DATE DEFAULT SYSDATE,
    description VARCHAR2(500)
) TABLESPACE &app_tablespace;

-- جدول الإعدادات
CREATE TABLE system_settings (
    id NUMBER(10) CONSTRAINT pk_system_settings PRIMARY KEY,
    setting_key VARCHAR2(100) NOT NULL CONSTRAINT uk_settings_key UNIQUE,
    setting_value CLOB,
    setting_type VARCHAR2(50) DEFAULT 'STRING',
    description VARCHAR2(500),
    category VARCHAR2(100),
    is_encrypted NUMBER(1) DEFAULT 0,
    modified_date DATE DEFAULT SYSDATE,
    modified_by VARCHAR2(100)
) TABLESPACE &app_tablespace;

-- إنشاء المتسلسلات (Sequences)
PROMPT إنشاء المتسلسلات...
CREATE SEQUENCE seq_roles START WITH 1 INCREMENT BY 1 CACHE 20;
CREATE SEQUENCE seq_users START WITH 1 INCREMENT BY 1 CACHE 20;
CREATE SEQUENCE seq_branches START WITH 1 INCREMENT BY 1 CACHE 20;
CREATE SEQUENCE seq_currencies START WITH 1 INCREMENT BY 1 CACHE 20;
CREATE SEQUENCE seq_customers START WITH 1 INCREMENT BY 1 CACHE 20;
CREATE SEQUENCE seq_shipments START WITH 1 INCREMENT BY 1 CACHE 20;
CREATE SEQUENCE seq_shipment_tracking START WITH 1 INCREMENT BY 1 CACHE 50;
CREATE SEQUENCE seq_audit_logs START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE seq_system_settings START WITH 1 INCREMENT BY 1 CACHE 20;

-- إنشاء الفهارس
PROMPT إنشاء الفهارس...
CREATE INDEX idx_users_username ON users(username) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_users_email ON users(email) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_users_role ON users(role_id) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_customers_code ON customers(code) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_customers_name ON customers(UPPER(name)) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_customers_type ON customers(customer_type) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_shipments_tracking ON shipments(tracking_number) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_shipments_customer ON shipments(customer_id) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_shipments_status ON shipments(status) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_shipments_date ON shipments(created_date) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_shipments_origin ON shipments(origin_branch_id) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_shipments_destination ON shipments(destination_branch_id) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_tracking_shipment ON shipment_tracking(shipment_id) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_tracking_status ON shipment_tracking(status) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_tracking_timestamp ON shipment_tracking(timestamp) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_audit_user ON audit_logs(user_id) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_audit_table ON audit_logs(table_name) TABLESPACE &app_index_tablespace;
CREATE INDEX idx_audit_timestamp ON audit_logs(timestamp) TABLESPACE &app_index_tablespace;

-- إدراج البيانات الأساسية
PROMPT إدراج البيانات الأساسية...

-- إدراج الأدوار الافتراضية
INSERT INTO roles (id, name, description, permissions, created_by) VALUES
(seq_roles.NEXTVAL, 'SUPER_ADMIN', 'مدير النظام الرئيسي - صلاحيات كاملة', 'ALL', 'SYSTEM');

INSERT INTO roles (id, name, description, permissions, created_by) VALUES
(seq_roles.NEXTVAL, 'ADMIN', 'مدير النظام - صلاحيات إدارية', 'ADMIN', 'SYSTEM');

INSERT INTO roles (id, name, description, permissions, created_by) VALUES
(seq_roles.NEXTVAL, 'MANAGER', 'مدير الفرع - صلاحيات إدارية محدودة', 'MANAGER', 'SYSTEM');

INSERT INTO roles (id, name, description, permissions, created_by) VALUES
(seq_roles.NEXTVAL, 'EMPLOYEE', 'موظف - صلاحيات تشغيلية', 'EMPLOYEE', 'SYSTEM');

INSERT INTO roles (id, name, description, permissions, created_by) VALUES
(seq_roles.NEXTVAL, 'VIEWER', 'مستعرض - صلاحيات قراءة فقط', 'VIEWER', 'SYSTEM');

-- إدراج العملات الافتراضية
INSERT INTO currencies (id, code, name, symbol, exchange_rate, is_default, created_by) VALUES
(seq_currencies.NEXTVAL, 'SAR', 'ريال سعودي', 'ر.س', 1.0, 1, 'SYSTEM');

INSERT INTO currencies (id, code, name, symbol, exchange_rate, created_by) VALUES
(seq_currencies.NEXTVAL, 'USD', 'دولار أمريكي', '$', 3.75, 'SYSTEM');

INSERT INTO currencies (id, code, name, symbol, exchange_rate, created_by) VALUES
(seq_currencies.NEXTVAL, 'EUR', 'يورو', '€', 4.10, 'SYSTEM');

INSERT INTO currencies (id, code, name, symbol, exchange_rate, created_by) VALUES
(seq_currencies.NEXTVAL, 'AED', 'درهم إماراتي', 'د.إ', 1.02, 'SYSTEM');

-- إدراج الفروع الافتراضية
INSERT INTO branches (id, code, name, address, city, region, country, phone, email, manager_name, created_by) VALUES
(seq_branches.NEXTVAL, 'MAIN', 'الفرع الرئيسي', 'شارع الملك فهد، حي العليا', 'الرياض', 'الرياض', 'المملكة العربية السعودية', '+966112345678', '<EMAIL>', 'أحمد محمد السعيد', 'SYSTEM');

INSERT INTO branches (id, code, name, address, city, region, country, phone, email, manager_name, created_by) VALUES
(seq_branches.NEXTVAL, 'JED', 'فرع جدة', 'شارع التحلية، حي الزهراء', 'جدة', 'مكة المكرمة', 'المملكة العربية السعودية', '+966126789012', '<EMAIL>', 'محمد علي الحربي', 'SYSTEM');

INSERT INTO branches (id, code, name, address, city, region, country, phone, email, manager_name, created_by) VALUES
(seq_branches.NEXTVAL, 'DAM', 'فرع الدمام', 'شارع الملك عبدالعزيز، حي الفيصلية', 'الدمام', 'الشرقية', 'المملكة العربية السعودية', '+966138901234', '<EMAIL>', 'عبدالله سالم القحطاني', 'SYSTEM');

-- إدراج المستخدمين الافتراضيين
INSERT INTO users (id, username, password_hash, email, full_name, phone, role_id, branch_id, created_by) VALUES
(seq_users.NEXTVAL, 'admin', 'admin123', '<EMAIL>', 'مدير النظام الرئيسي', '+966501234567', 1, 1, 'SYSTEM');

INSERT INTO users (id, username, password_hash, email, full_name, phone, role_id, branch_id, created_by) VALUES
(seq_users.NEXTVAL, 'manager', 'manager123', '<EMAIL>', 'مدير العمليات', '+966502345678', 3, 1, 'SYSTEM');

-- إدراج عملاء تجريبيين
INSERT INTO customers (id, code, name, customer_type, email, phone, mobile, address, city, region, preferred_currency_id, created_by) VALUES
(seq_customers.NEXTVAL, 'CUST001', 'شركة النقل المتطور', 'COMPANY', '<EMAIL>', '+966112223333', '+966501112222', 'شارع الأمير سلطان، الرياض', 'الرياض', 'الرياض', 1, 'SYSTEM');

INSERT INTO customers (id, code, name, customer_type, email, phone, mobile, address, city, region, preferred_currency_id, created_by) VALUES
(seq_customers.NEXTVAL, 'CUST002', 'أحمد محمد العلي', 'INDIVIDUAL', '<EMAIL>', '+966126667777', '+966503334444', 'حي الصفا، جدة', 'جدة', 'مكة المكرمة', 1, 'SYSTEM');

-- إدراج الإعدادات الأساسية
INSERT INTO system_settings (id, setting_key, setting_value, setting_type, description, category, modified_by) VALUES
(seq_system_settings.NEXTVAL, 'COMPANY_NAME', 'شركة الشحن المتطور', 'STRING', 'اسم الشركة', 'GENERAL', 'SYSTEM');

INSERT INTO system_settings (id, setting_key, setting_value, setting_type, description, category, modified_by) VALUES
(seq_system_settings.NEXTVAL, 'COMPANY_ADDRESS', 'الرياض، المملكة العربية السعودية', 'STRING', 'عنوان الشركة', 'GENERAL', 'SYSTEM');

INSERT INTO system_settings (id, setting_key, setting_value, setting_type, description, category, modified_by) VALUES
(seq_system_settings.NEXTVAL, 'COMPANY_PHONE', '+966112345678', 'STRING', 'هاتف الشركة', 'GENERAL', 'SYSTEM');

INSERT INTO system_settings (id, setting_key, setting_value, setting_type, description, category, modified_by) VALUES
(seq_system_settings.NEXTVAL, 'COMPANY_EMAIL', '<EMAIL>', 'STRING', 'بريد الشركة الإلكتروني', 'GENERAL', 'SYSTEM');

INSERT INTO system_settings (id, setting_key, setting_value, setting_type, description, category, modified_by) VALUES
(seq_system_settings.NEXTVAL, 'DEFAULT_CURRENCY', 'SAR', 'STRING', 'العملة الافتراضية', 'FINANCIAL', 'SYSTEM');

INSERT INTO system_settings (id, setting_key, setting_value, setting_type, description, category, modified_by) VALUES
(seq_system_settings.NEXTVAL, 'TAX_RATE', '15', 'NUMBER', 'معدل الضريبة المضافة (%)', 'FINANCIAL', 'SYSTEM');

INSERT INTO system_settings (id, setting_key, setting_value, setting_type, description, category, modified_by) VALUES
(seq_system_settings.NEXTVAL, 'AUTO_TRACKING_UPDATE', 'true', 'BOOLEAN', 'تحديث تتبع الشحنات تلقائياً', 'SYSTEM', 'SYSTEM');

INSERT INTO system_settings (id, setting_key, setting_value, setting_type, description, category, modified_by) VALUES
(seq_system_settings.NEXTVAL, 'EMAIL_NOTIFICATIONS', 'true', 'BOOLEAN', 'إرسال إشعارات البريد الإلكتروني', 'NOTIFICATIONS', 'SYSTEM');

INSERT INTO system_settings (id, setting_key, setting_value, setting_type, description, category, modified_by) VALUES
(seq_system_settings.NEXTVAL, 'SMS_NOTIFICATIONS', 'true', 'BOOLEAN', 'إرسال إشعارات الرسائل النصية', 'NOTIFICATIONS', 'SYSTEM');

COMMIT;

PROMPT ========================================
PROMPT تم إدراج البيانات الأساسية بنجاح
PROMPT Initial data inserted successfully
PROMPT ========================================

PROMPT ========================================
PROMPT تم إعداد قاعدة البيانات بنجاح!
PROMPT Database setup completed successfully!
PROMPT ========================================
PROMPT معلومات الاتصال:
PROMPT Connection Information:
PROMPT المستخدم/User: &app_user
PROMPT كلمة المرور/Password: &app_password
PROMPT Tablespace: &app_tablespace
PROMPT Index Tablespace: &app_index_tablespace
PROMPT ========================================
PROMPT إحصائيات قاعدة البيانات:
PROMPT Database Statistics:
PROMPT ========================================

-- عرض إحصائيات الجداول
SELECT 'الأدوار/Roles: ' || COUNT(*) AS info FROM roles
UNION ALL
SELECT 'المستخدمين/Users: ' || COUNT(*) FROM users
UNION ALL
SELECT 'الفروع/Branches: ' || COUNT(*) FROM branches
UNION ALL
SELECT 'العملات/Currencies: ' || COUNT(*) FROM currencies
UNION ALL
SELECT 'العملاء/Customers: ' || COUNT(*) FROM customers
UNION ALL
SELECT 'الإعدادات/Settings: ' || COUNT(*) FROM system_settings;

PROMPT ========================================
PROMPT انتهى إعداد قاعدة البيانات بنجاح!
PROMPT Database setup finished successfully!
PROMPT ========================================

EXIT;
