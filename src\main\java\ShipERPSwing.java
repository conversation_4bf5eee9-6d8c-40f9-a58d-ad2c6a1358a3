import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import com.formdev.flatlaf.FlatLightLaf;

/**
 * تطبيق إدارة الشحنات باستخدام Swing
 */
public class ShipERPSwing extends JFrame {
    
    private JLabel statusLabel;
    private JLabel timeLabel;
    private JPanel mainPanel;
    
    public ShipERPSwing() {
        initializeUI();
    }
    
    private void initializeUI() {
        // إعداد المظهر
        try {
            UIManager.setLookAndFeel(new FlatLightLaf());
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        setTitle("نظام إدارة الشحنات - Ship ERP System");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        
        // إنشاء القائمة الرئيسية
        createMenuBar();
        
        // إنشاء المحتوى الرئيسي
        createMainContent();
        
        // إنشاء شريط الحالة
        createStatusBar();
        
        // إظهار رسالة ترحيب
        SwingUtilities.invokeLater(this::showWelcomeMessage);
        
        // تحديث الوقت كل ثانية
        Timer timer = new Timer(1000, e -> updateTime());
        timer.start();
    }
    
    private void createMenuBar() {
        JMenuBar menuBar = new JMenuBar();
        
        // قائمة الملف
        JMenu fileMenu = new JMenu("ملف");
        fileMenu.add(createMenuItem("جديد", "icons/new.png"));
        fileMenu.add(createMenuItem("فتح", "icons/open.png"));
        fileMenu.add(createMenuItem("حفظ", "icons/save.png"));
        fileMenu.addSeparator();
        JMenuItem exitItem = createMenuItem("خروج", "icons/exit.png");
        exitItem.addActionListener(e -> System.exit(0));
        fileMenu.add(exitItem);
        
        // قائمة الشحنات
        JMenu shipmentMenu = new JMenu("الشحنات");
        shipmentMenu.add(createMenuItem("شحنة جديدة", "icons/shipment.png"));
        shipmentMenu.add(createMenuItem("تتبع الشحنات", "icons/track.png"));
        shipmentMenu.add(createMenuItem("تقارير الشحنات", "icons/report.png"));
        
        // قائمة العملاء
        JMenu customerMenu = new JMenu("العملاء");
        customerMenu.add(createMenuItem("عميل جديد", "icons/customer.png"));
        customerMenu.add(createMenuItem("قائمة العملاء", "icons/customers.png"));
        
        // قائمة التقارير
        JMenu reportsMenu = new JMenu("التقارير");
        reportsMenu.add(createMenuItem("تقرير يومي", "icons/daily.png"));
        reportsMenu.add(createMenuItem("تقرير شهري", "icons/monthly.png"));
        reportsMenu.add(createMenuItem("تقرير سنوي", "icons/yearly.png"));
        
        // قائمة الإعدادات
        JMenu settingsMenu = new JMenu("الإعدادات");
        settingsMenu.add(createMenuItem("إعدادات النظام", "icons/settings.png"));
        settingsMenu.add(createMenuItem("إدارة المستخدمين", "icons/users.png"));
        settingsMenu.add(createMenuItem("النسخ الاحتياطي", "icons/backup.png"));
        
        // قائمة المساعدة
        JMenu helpMenu = new JMenu("مساعدة");
        JMenuItem aboutItem = createMenuItem("حول البرنامج", "icons/about.png");
        aboutItem.addActionListener(e -> showAboutDialog());
        helpMenu.add(aboutItem);
        helpMenu.add(createMenuItem("دليل المستخدم", "icons/help.png"));
        
        menuBar.add(fileMenu);
        menuBar.add(shipmentMenu);
        menuBar.add(customerMenu);
        menuBar.add(reportsMenu);
        menuBar.add(settingsMenu);
        menuBar.add(helpMenu);
        
        setJMenuBar(menuBar);
    }
    
    private JMenuItem createMenuItem(String text, String iconPath) {
        JMenuItem item = new JMenuItem(text);
        // يمكن إضافة الأيقونات لاحقاً
        return item;
    }
    
    private void createMainContent() {
        mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(new EmptyBorder(20, 20, 20, 20));
        
        // العنوان الرئيسي
        JLabel titleLabel = new JLabel("مرحباً بك في نظام إدارة الشحنات", JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setForeground(new Color(44, 62, 80));
        titleLabel.setBorder(new EmptyBorder(0, 0, 30, 0));
        
        // الأزرار السريعة
        JPanel buttonPanel = createQuickButtonsPanel();
        
        // معلومات سريعة
        JPanel infoPanel = createInfoPanel();
        
        // تجميع المحتوى
        JPanel centerPanel = new JPanel(new BorderLayout());
        centerPanel.add(titleLabel, BorderLayout.NORTH);
        centerPanel.add(buttonPanel, BorderLayout.CENTER);
        centerPanel.add(infoPanel, BorderLayout.SOUTH);
        
        mainPanel.add(centerPanel, BorderLayout.CENTER);
        add(mainPanel, BorderLayout.CENTER);
    }
    
    private JPanel createQuickButtonsPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 2, 20, 20));
        panel.setBorder(new EmptyBorder(20, 50, 20, 50));
        
        // شحنة جديدة
        JButton newShipmentBtn = createStyledButton("شحنة جديدة", new Color(52, 152, 219));
        newShipmentBtn.addActionListener(e -> showMessage("فتح نافذة شحنة جديدة"));
        
        // تتبع الشحنات
        JButton trackBtn = createStyledButton("تتبع الشحنات", new Color(46, 204, 113));
        trackBtn.addActionListener(e -> showMessage("فتح نافذة تتبع الشحنات"));
        
        // إدارة العملاء
        JButton customersBtn = createStyledButton("إدارة العملاء", new Color(231, 76, 60));
        customersBtn.addActionListener(e -> showMessage("فتح نافذة إدارة العملاء"));
        
        // التقارير
        JButton reportsBtn = createStyledButton("التقارير", new Color(243, 156, 18));
        reportsBtn.addActionListener(e -> showMessage("فتح نافذة التقارير"));
        
        panel.add(newShipmentBtn);
        panel.add(trackBtn);
        panel.add(customersBtn);
        panel.add(reportsBtn);
        
        return panel;
    }
    
    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setPreferredSize(new Dimension(200, 80));
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFont(new Font("Arial", Font.BOLD, 16));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        // تأثير hover
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(color.darker());
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(color);
            }
        });
        
        return button;
    }
    
    private JPanel createInfoPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 3, 20, 10));
        panel.setBorder(BorderFactory.createTitledBorder("إحصائيات سريعة"));
        panel.setBorder(new EmptyBorder(30, 50, 20, 50));
        
        // إحصائيات اليوم
        panel.add(createInfoLabel("عدد الشحنات اليوم:", "25", new Color(52, 152, 219)));
        panel.add(createInfoLabel("الشحنات المعلقة:", "8", new Color(231, 76, 60)));
        panel.add(createInfoLabel("الشحنات المسلمة:", "17", new Color(46, 204, 113)));
        panel.add(createInfoLabel("إجمالي العملاء:", "156", new Color(155, 89, 182)));
        panel.add(createInfoLabel("الإيرادات اليوم:", "12,500 ريال", new Color(243, 156, 18)));
        panel.add(createInfoLabel("متوسط وقت التسليم:", "2.5 يوم", new Color(52, 73, 94)));
        
        return panel;
    }
    
    private JPanel createInfoLabel(String title, String value, Color color) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY, 1));
        panel.setBackground(Color.WHITE);
        
        JLabel titleLabel = new JLabel(title, JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.PLAIN, 12));
        titleLabel.setBorder(new EmptyBorder(5, 5, 0, 5));
        
        JLabel valueLabel = new JLabel(value, JLabel.CENTER);
        valueLabel.setFont(new Font("Arial", Font.BOLD, 18));
        valueLabel.setForeground(color);
        valueLabel.setBorder(new EmptyBorder(0, 5, 5, 5));
        
        panel.add(titleLabel, BorderLayout.NORTH);
        panel.add(valueLabel, BorderLayout.CENTER);
        
        return panel;
    }
    
    private void createStatusBar() {
        JPanel statusBar = new JPanel(new BorderLayout());
        statusBar.setBorder(BorderFactory.createRaisedBevelBorder());
        statusBar.setPreferredSize(new Dimension(getWidth(), 25));
        
        statusLabel = new JLabel("جاهز");
        statusLabel.setBorder(new EmptyBorder(2, 10, 2, 10));
        
        JLabel userLabel = new JLabel("المستخدم: مدير النظام");
        userLabel.setBorder(new EmptyBorder(2, 10, 2, 10));
        
        timeLabel = new JLabel();
        timeLabel.setBorder(new EmptyBorder(2, 10, 2, 10));
        updateTime();
        
        statusBar.add(statusLabel, BorderLayout.WEST);
        statusBar.add(userLabel, BorderLayout.CENTER);
        statusBar.add(timeLabel, BorderLayout.EAST);
        
        add(statusBar, BorderLayout.SOUTH);
    }
    
    private void updateTime() {
        String currentTime = LocalDateTime.now().format(
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        timeLabel.setText("الوقت: " + currentTime);
    }
    
    private void showWelcomeMessage() {
        JOptionPane.showMessageDialog(this,
            "مرحباً بك في نظام إدارة الشحنات المتطور!\n\n" +
            "النظام جاهز للاستخدام.\n" +
            "يمكنك البدء بإنشاء شحنة جديدة أو تتبع الشحنات الموجودة.",
            "مرحباً",
            JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void showAboutDialog() {
        JOptionPane.showMessageDialog(this,
            "نظام إدارة الشحنات\n\n" +
            "الإصدار: 1.0.0\n" +
            "تطوير: فريق التطوير\n" +
            "حقوق الطبع محفوظة © 2025\n\n" +
            "نظام متكامل لإدارة الشحنات والعملاء والتقارير",
            "حول البرنامج",
            JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void showMessage(String message) {
        statusLabel.setText(message);
        JOptionPane.showMessageDialog(this, message, "معلومات", JOptionPane.INFORMATION_MESSAGE);
        statusLabel.setText("جاهز");
    }
    
    public static void main(String[] args) {
        // إعداد الخط العربي
        System.setProperty("file.encoding", "UTF-8");
        
        SwingUtilities.invokeLater(() -> {
            try {
                new ShipERPSwing().setVisible(true);
            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null, 
                    "خطأ في تشغيل التطبيق: " + e.getMessage(), 
                    "خطأ", 
                    JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
