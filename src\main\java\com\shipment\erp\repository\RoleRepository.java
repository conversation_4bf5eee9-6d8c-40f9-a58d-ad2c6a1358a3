package com.shipment.erp.repository;

import com.shipment.erp.model.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * مستودع الأدوار
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    
    /**
     * البحث عن دور بالاسم
     */
    Optional<Role> findByName(String name);
    
    /**
     * التحقق من وجود دور بالاسم
     */
    boolean existsByName(String name);
    
    /**
     * الحصول على الأدوار النشطة
     */
    List<Role> findByActiveTrue();
    
    /**
     * الحصول على الأدوار غير النشطة
     */
    List<Role> findByActiveFalse();
    
    /**
     * البحث عن الأدوار بالاسم (يحتوي على)
     */
    List<Role> findByNameContainingIgnoreCase(String name);
    
    /**
     * الحصول على الأدوار مرتبة بالاسم
     */
    List<Role> findAllByOrderByNameAsc();
}
