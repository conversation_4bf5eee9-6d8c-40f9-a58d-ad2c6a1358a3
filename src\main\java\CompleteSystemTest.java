import javax.swing.*;
import java.awt.*;
import java.util.Locale;

/**
 * اختبار النظام الكامل مع جميع الميزات
 * Complete System Test with All Features
 */
public class CompleteSystemTest {
    
    public static void main(String[] args) {
        // تعيين الترميز والمحلية العربية
        System.setProperty("file.encoding", "UTF-8");
        Locale.setDefault(new Locale("ar", "SA"));
        
        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق المظهر المحفوظ
                SettingsManager.applyStoredTheme();
                
                // عرض شاشة البداية
                showSplashScreen();
                
                // إنشاء النافذة الرئيسية المحسنة
                EnhancedMainWindow mainWindow = new EnhancedMainWindow();
                mainWindow.setVisible(true);
                
                System.out.println("=== تم تشغيل النظام الكامل بنجاح! ===");
                System.out.println("الميزات المتاحة:");
                System.out.println("• القائمة الشجرية المتقدمة");
                System.out.println("• نظام البحث الفوري");
                System.out.println("• إدارة المستخدمين الشاملة");
                System.out.println("• الإعدادات العامة المتقدمة");
                System.out.println("• مكتبة الأدوات والأيقونات");
                System.out.println("• دعم كامل للغة العربية");
                
            } catch (Exception e) {
                e.printStackTrace();
                UIUtils.showErrorMessage(null, "حدث خطأ في تشغيل التطبيق: " + e.getMessage());
            }
        });
    }
    
    /**
     * عرض شاشة البداية
     */
    private static void showSplashScreen() {
        JWindow splash = new JWindow();
        splash.setSize(500, 300);
        splash.setLocationRelativeTo(null);
        
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(UIUtils.PRIMARY_COLOR);
        panel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));
        
        // العنوان
        JLabel titleLabel = new JLabel("نظام إدارة الشحنات المتقدم");
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 24));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // العنوان الفرعي
        JLabel subtitleLabel = new JLabel("الإصدار 2.0 - واجهة شجرية متقدمة");
        subtitleLabel.setFont(UIUtils.ARABIC_FONT_LARGE);
        subtitleLabel.setForeground(new Color(236, 240, 241));
        subtitleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        subtitleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // شريط التقدم
        JProgressBar progressBar = UIUtils.createStyledProgressBar();
        progressBar.setIndeterminate(true);
        progressBar.setString("جاري التحميل...");
        
        // الميزات
        JPanel featuresPanel = new JPanel(new GridLayout(3, 2, 10, 5));
        featuresPanel.setOpaque(false);
        featuresPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        String[] features = {
            "• قائمة شجرية تفاعلية", "• بحث فوري متقدم",
            "• إدارة مستخدمين شاملة", "• إعدادات متقدمة",
            "• دعم كامل للعربية", "• واجهة احترافية"
        };
        
        for (String feature : features) {
            JLabel featureLabel = new JLabel(feature);
            featureLabel.setFont(UIUtils.ARABIC_FONT);
            featureLabel.setForeground(Color.WHITE);
            featureLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            featuresPanel.add(featureLabel);
        }
        
        JPanel textPanel = new JPanel(new GridLayout(2, 1, 0, 10));
        textPanel.setOpaque(false);
        textPanel.add(titleLabel);
        textPanel.add(subtitleLabel);
        
        panel.add(textPanel, BorderLayout.NORTH);
        panel.add(featuresPanel, BorderLayout.CENTER);
        panel.add(progressBar, BorderLayout.SOUTH);
        
        splash.add(panel);
        splash.setVisible(true);
        
        // إخفاء شاشة البداية بعد 3 ثوان
        Timer timer = new Timer(3000, e -> splash.dispose());
        timer.setRepeats(false);
        timer.start();
        
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
