@echo off
chcp 65001 > nul
echo ========================================
echo    اختبار اتصال قاعدة البيانات
echo    Database Connection Test
echo ========================================
echo.

REM إعداد متغيرات البيئة
set SCRIPT_DIR=%~dp0
set CONFIG_FILE=%SCRIPT_DIR%database-config.properties
set TEST_SQL=%SCRIPT_DIR%scripts\test-connection.sql

echo [INFO] بدء اختبار اتصال قاعدة البيانات...
echo.

REM التحقق من وجود Oracle SQL*Plus
echo [INFO] التحقق من تثبيت Oracle...
sqlplus -v > nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Oracle SQL*Plus غير مثبت أو غير موجود في PATH
    echo [INFO] يرجى تثبيت Oracle Database أو Oracle Client
    pause
    exit /b 1
)

echo [INFO] تم العثور على Oracle SQL*Plus ✓
echo.

REM قراءة إعدادات الاتصال
if exist "%CONFIG_FILE%" (
    echo [INFO] قراءة إعدادات الاتصال من: %CONFIG_FILE%
    for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
        if "%%a"=="db.host" set DB_HOST=%%b
        if "%%a"=="db.port" set DB_PORT=%%b
        if "%%a"=="db.sid" set DB_SID=%%b
        if "%%a"=="db.username" set DB_USER=%%b
        if "%%a"=="db.password" set DB_PASS=%%b
    )
    echo   الخادم: %DB_HOST%
    echo   المنفذ: %DB_PORT%
    echo   قاعدة البيانات: %DB_SID%
    echo   المستخدم: %DB_USER%
) else (
    echo [WARN] ملف إعدادات قاعدة البيانات غير موجود
    echo [INFO] استخدام الإعدادات الافتراضية...
    set DB_HOST=localhost
    set DB_PORT=1521
    set DB_SID=orcl
    set DB_USER=ship_erp
    set DB_PASS=ship_erp_password
)
echo.

REM إنشاء ملف اختبار SQL
echo [INFO] إنشاء ملف اختبار SQL...
echo -- اختبار اتصال قاعدة البيانات > "%TEST_SQL%"
echo SET ECHO ON >> "%TEST_SQL%"
echo SET FEEDBACK ON >> "%TEST_SQL%"
echo SET SERVEROUTPUT ON >> "%TEST_SQL%"
echo. >> "%TEST_SQL%"
echo PROMPT ======================================== >> "%TEST_SQL%"
echo PROMPT اختبار اتصال قاعدة البيانات >> "%TEST_SQL%"
echo PROMPT Database Connection Test >> "%TEST_SQL%"
echo PROMPT ======================================== >> "%TEST_SQL%"
echo. >> "%TEST_SQL%"
echo -- عرض معلومات قاعدة البيانات >> "%TEST_SQL%"
echo SELECT 'اسم قاعدة البيانات: ' ^|^| name AS info FROM v$database; >> "%TEST_SQL%"
echo SELECT 'إصدار Oracle: ' ^|^| version AS info FROM v$instance; >> "%TEST_SQL%"
echo SELECT 'المستخدم الحالي: ' ^|^| USER AS info FROM dual; >> "%TEST_SQL%"
echo SELECT 'التاريخ والوقت: ' ^|^| TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') AS info FROM dual; >> "%TEST_SQL%"
echo. >> "%TEST_SQL%"
echo -- اختبار الجداول >> "%TEST_SQL%"
echo PROMPT ======================================== >> "%TEST_SQL%"
echo PROMPT اختبار وجود الجداول >> "%TEST_SQL%"
echo PROMPT Testing Tables Existence >> "%TEST_SQL%"
echo PROMPT ======================================== >> "%TEST_SQL%"
echo. >> "%TEST_SQL%"
echo SELECT 'جدول ' ^|^| table_name ^|^| ': موجود (' ^|^| num_rows ^|^| ' صف)' AS table_info >> "%TEST_SQL%"
echo FROM user_tables >> "%TEST_SQL%"
echo WHERE table_name IN ('ROLES', 'USERS', 'BRANCHES', 'CURRENCIES', 'CUSTOMERS', 'SHIPMENTS') >> "%TEST_SQL%"
echo ORDER BY table_name; >> "%TEST_SQL%"
echo. >> "%TEST_SQL%"
echo -- اختبار البيانات الأساسية >> "%TEST_SQL%"
echo PROMPT ======================================== >> "%TEST_SQL%"
echo PROMPT اختبار البيانات الأساسية >> "%TEST_SQL%"
echo PROMPT Testing Basic Data >> "%TEST_SQL%"
echo PROMPT ======================================== >> "%TEST_SQL%"
echo. >> "%TEST_SQL%"
echo SELECT 'الأدوار: ' ^|^| COUNT(*) AS info FROM roles; >> "%TEST_SQL%"
echo SELECT 'المستخدمين: ' ^|^| COUNT(*) AS info FROM users; >> "%TEST_SQL%"
echo SELECT 'الفروع: ' ^|^| COUNT(*) AS info FROM branches; >> "%TEST_SQL%"
echo SELECT 'العملات: ' ^|^| COUNT(*) AS info FROM currencies; >> "%TEST_SQL%"
echo SELECT 'العملاء: ' ^|^| COUNT(*) AS info FROM customers; >> "%TEST_SQL%"
echo. >> "%TEST_SQL%"
echo -- اختبار المتسلسلات >> "%TEST_SQL%"
echo PROMPT ======================================== >> "%TEST_SQL%"
echo PROMPT اختبار المتسلسلات >> "%TEST_SQL%"
echo PROMPT Testing Sequences >> "%TEST_SQL%"
echo PROMPT ======================================== >> "%TEST_SQL%"
echo. >> "%TEST_SQL%"
echo SELECT sequence_name ^|^| ': ' ^|^| last_number AS sequence_info >> "%TEST_SQL%"
echo FROM user_sequences >> "%TEST_SQL%"
echo WHERE sequence_name LIKE 'SEQ_%%' >> "%TEST_SQL%"
echo ORDER BY sequence_name; >> "%TEST_SQL%"
echo. >> "%TEST_SQL%"
echo -- اختبار الفهارس >> "%TEST_SQL%"
echo PROMPT ======================================== >> "%TEST_SQL%"
echo PROMPT اختبار الفهارس >> "%TEST_SQL%"
echo PROMPT Testing Indexes >> "%TEST_SQL%"
echo PROMPT ======================================== >> "%TEST_SQL%"
echo. >> "%TEST_SQL%"
echo SELECT 'فهرس ' ^|^| index_name ^|^| ' على جدول ' ^|^| table_name AS index_info >> "%TEST_SQL%"
echo FROM user_indexes >> "%TEST_SQL%"
echo WHERE index_name LIKE 'IDX_%%' >> "%TEST_SQL%"
echo ORDER BY table_name, index_name; >> "%TEST_SQL%"
echo. >> "%TEST_SQL%"
echo PROMPT ======================================== >> "%TEST_SQL%"
echo PROMPT انتهى اختبار قاعدة البيانات >> "%TEST_SQL%"
echo PROMPT Database Test Completed >> "%TEST_SQL%"
echo PROMPT ======================================== >> "%TEST_SQL%"
echo. >> "%TEST_SQL%"
echo EXIT; >> "%TEST_SQL%"

echo [INFO] تم إنشاء ملف الاختبار ✓
echo.

REM تشغيل اختبار الاتصال
echo [INFO] بدء اختبار الاتصال...
echo [INFO] الاتصال بـ: %DB_USER%@%DB_HOST%:%DB_PORT%/%DB_SID%
echo.

sqlplus %DB_USER%/%DB_PASS%@%DB_HOST%:%DB_PORT%/%DB_SID% @"%TEST_SQL%"

set TEST_RESULT=%errorlevel%

echo.
echo ========================================
if %TEST_RESULT% equ 0 (
    echo [SUCCESS] ✓ اختبار الاتصال نجح!
    echo.
    echo [INFO] قاعدة البيانات تعمل بشكل صحيح
    echo [INFO] جميع الجداول والبيانات متوفرة
    echo [INFO] يمكنك الآن تشغيل التطبيق باستخدام: run-ship-erp.bat
) else (
    echo [ERROR] ✗ فشل اختبار الاتصال
    echo.
    echo [INFO] الأسباب المحتملة:
    echo   ✗ معلومات اتصال خاطئة
    echo   ✗ خدمة Oracle Database غير متاحة
    echo   ✗ المستخدم ship_erp غير موجود
    echo   ✗ كلمة مرور خاطئة
    echo   ✗ قاعدة البيانات غير مُعدة
    echo.
    echo [INFO] للحل:
    echo   1. تأكد من تشغيل خدمة Oracle Database
    echo   2. تحقق من معلومات الاتصال في: %CONFIG_FILE%
    echo   3. قم بإعداد قاعدة البيانات باستخدام: setup-database-enhanced.bat
    echo   4. تأكد من وجود المستخدم ship_erp
)
echo ========================================
echo.

REM تنظيف الملفات المؤقتة
if exist "%TEST_SQL%" del "%TEST_SQL%"

pause
