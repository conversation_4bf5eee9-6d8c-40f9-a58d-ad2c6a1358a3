@echo off
chcp 65001 > nul
echo ========================================
echo    تشغيل نظام إدارة الشحنات
echo    Ship ERP System Launcher
echo ========================================
echo.

REM إعداد متغيرات البيئة
set SCRIPT_DIR=%~dp0
set JAR_FILE=%SCRIPT_DIR%target\ship-erp-system-1.0.0.jar
set CONFIG_FILE=%SCRIPT_DIR%database-config.properties
set LOG_DIR=%SCRIPT_DIR%logs
set JAVA_OPTS=-Xms512m -Xmx2048m -Dfile.encoding=UTF-8

echo [INFO] بدء تشغيل نظام إدارة الشحنات...
echo [INFO] مجلد التطبيق: %SCRIPT_DIR%
echo.

REM التحقق من وجود Java
echo [INFO] التحقق من تثبيت Java...
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java غير مثبت أو غير موجود في PATH
    echo [INFO] يرجى تثبيت Java 17 أو أحدث
    echo [INFO] للتحميل: https://www.oracle.com/java/technologies/downloads/
    pause
    exit /b 1
)

echo [INFO] تم العثور على Java ✓
java -version 2>&1 | findstr "version"
echo.

REM التحقق من وجود ملف JAR
if not exist "%JAR_FILE%" (
    echo [ERROR] ملف التطبيق غير موجود: %JAR_FILE%
    echo [INFO] يرجى تجميع التطبيق أولاً باستخدام:
    echo   mvn clean package
    echo أو
    echo   D:\java\tools\apache-maven-3.9.6\bin\mvn.cmd clean package
    pause
    exit /b 1
)

echo [INFO] تم العثور على ملف التطبيق ✓
echo [INFO] حجم الملف: 
for %%A in ("%JAR_FILE%") do echo   %%~zA bytes
echo.

REM التحقق من وجود ملف إعدادات قاعدة البيانات
if exist "%CONFIG_FILE%" (
    echo [INFO] تم العثور على ملف إعدادات قاعدة البيانات ✓
    echo [INFO] قراءة إعدادات الاتصال من: %CONFIG_FILE%
    for /f "tokens=1,2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr /v "^#" ^| findstr "="') do (
        if "%%a"=="db.host" echo   الخادم: %%b
        if "%%a"=="db.port" echo   المنفذ: %%b
        if "%%a"=="db.sid" echo   قاعدة البيانات: %%b
        if "%%a"=="db.username" echo   المستخدم: %%b
    )
) else (
    echo [WARN] ملف إعدادات قاعدة البيانات غير موجود
    echo [INFO] سيتم استخدام الإعدادات الافتراضية من application.properties
    echo [INFO] للحصول على ملف الإعدادات، قم بتشغيل: setup-database-enhanced.bat
)
echo.

REM إنشاء مجلد السجلات إذا لم يكن موجوداً
if not exist "%LOG_DIR%" (
    echo [INFO] إنشاء مجلد السجلات...
    mkdir "%LOG_DIR%"
)

REM التحقق من اتصال قاعدة البيانات (اختياري)
echo [INFO] اختبار اتصال قاعدة البيانات...
if exist "%CONFIG_FILE%" (
    for /f "tokens=2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr "db.host="') do set DB_HOST=%%a
    for /f "tokens=2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr "db.port="') do set DB_PORT=%%a
    for /f "tokens=2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr "db.sid="') do set DB_SID=%%a
    for /f "tokens=2 delims==" %%a in ('type "%CONFIG_FILE%" ^| findstr "db.username="') do set DB_USER=%%a
    
    REM اختبار الاتصال باستخدام tnsping (إذا كان متوفراً)
    tnsping %DB_HOST%:%DB_PORT%/%DB_SID% > nul 2>&1
    if %errorlevel% equ 0 (
        echo [INFO] اتصال قاعدة البيانات متاح ✓
    ) else (
        echo [WARN] لا يمكن التحقق من اتصال قاعدة البيانات
        echo [INFO] تأكد من تشغيل خدمة Oracle Database
    )
) else (
    echo [INFO] تخطي اختبار الاتصال (لا يوجد ملف إعدادات)
)
echo.

REM عرض خيارات التشغيل
echo [INFO] خيارات التشغيل:
echo   1. تشغيل عادي
echo   2. تشغيل مع تفعيل السجلات التفصيلية
echo   3. تشغيل في وضع التطوير
echo   4. تشغيل مع واجهة وحدة التحكم فقط
echo   5. إعادة إنشاء قاعدة البيانات والتشغيل
echo.

set /p RUN_MODE="اختر وضع التشغيل [1]: "
if "%RUN_MODE%"=="" set RUN_MODE=1

echo.

REM تحديد معاملات التشغيل حسب الوضع المختار
if "%RUN_MODE%"=="1" (
    echo [INFO] تشغيل عادي...
    set JAVA_OPTS=%JAVA_OPTS% -Dspring.profiles.active=production
) else if "%RUN_MODE%"=="2" (
    echo [INFO] تشغيل مع تفعيل السجلات التفصيلية...
    set JAVA_OPTS=%JAVA_OPTS% -Dspring.profiles.active=production -Dlogging.level.com.shipment.erp=DEBUG -Dlogging.level.org.hibernate.SQL=DEBUG
) else if "%RUN_MODE%"=="3" (
    echo [INFO] تشغيل في وضع التطوير...
    set JAVA_OPTS=%JAVA_OPTS% -Dspring.profiles.active=development -Dspring.devtools.restart.enabled=true
) else if "%RUN_MODE%"=="4" (
    echo [INFO] تشغيل مع واجهة وحدة التحكم فقط...
    set JAVA_OPTS=%JAVA_OPTS% -Dspring.profiles.active=console -Djava.awt.headless=true
) else if "%RUN_MODE%"=="5" (
    echo [INFO] إعادة إنشاء قاعدة البيانات...
    call setup-database-enhanced.bat
    if %errorlevel% neq 0 (
        echo [ERROR] فشل في إعداد قاعدة البيانات
        pause
        exit /b 1
    )
    set JAVA_OPTS=%JAVA_OPTS% -Dspring.profiles.active=production
) else (
    echo [WARN] وضع غير صحيح، سيتم استخدام الوضع العادي
    set JAVA_OPTS=%JAVA_OPTS% -Dspring.profiles.active=production
)

echo.
echo [INFO] معاملات Java: %JAVA_OPTS%
echo.

REM تشغيل التطبيق
echo [INFO] بدء تشغيل التطبيق...
echo [INFO] للإيقاف: اضغط Ctrl+C
echo.
echo ========================================
echo    نظام إدارة الشحنات يعمل الآن
echo    Ship ERP System is Running
echo ========================================
echo.

REM تشغيل التطبيق مع معالجة الأخطاء
java %JAVA_OPTS% -jar "%JAR_FILE%"

set APP_RESULT=%errorlevel%

echo.
echo ========================================
if %APP_RESULT% equ 0 (
    echo [INFO] تم إغلاق التطبيق بنجاح
) else (
    echo [ERROR] التطبيق توقف بخطأ (كود الخطأ: %APP_RESULT%)
    echo.
    echo [INFO] الأسباب المحتملة:
    echo   ✗ مشكلة في اتصال قاعدة البيانات
    echo   ✗ منفذ التطبيق مستخدم بالفعل
    echo   ✗ ذاكرة غير كافية
    echo   ✗ ملفات إعدادات خاطئة
    echo.
    echo [INFO] للمساعدة:
    echo   1. تحقق من ملف السجل: %LOG_DIR%\ship-erp.log
    echo   2. تأكد من تشغيل خدمة Oracle Database
    echo   3. تحقق من إعدادات قاعدة البيانات
    echo   4. تأكد من عدم تشغيل نسخة أخرى من التطبيق
)
echo ========================================
echo.

pause
