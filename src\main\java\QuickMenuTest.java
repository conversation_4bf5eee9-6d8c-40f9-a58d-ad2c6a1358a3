import javax.swing.*;
import java.awt.*;
import java.util.Locale;

/**
 * اختبار سريع للقائمة بالعرض الجديد 420 بكسل
 * Quick Test for Menu with New 420px Width
 */
public class QuickMenuTest {
    
    public static void main(String[] args) {
        // تعيين الترميز والمحلية العربية
        System.setProperty("file.encoding", "UTF-8");
        Locale.setDefault(new Locale("ar", "SA"));
        
        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق المظهر المحفوظ
                SettingsManager.applyStoredTheme();
                
                // إنشاء النافذة الرئيسية المحسنة
                EnhancedMainWindow mainWindow = new EnhancedMainWindow();
                mainWindow.setVisible(true);
                
                System.out.println("=== تم تشغيل النظام مع القائمة المحسنة! ===");
                System.out.println("عرض القائمة الجديد: 420 بكسل");
                System.out.println("الميزات:");
                System.out.println("• قائمة أوسع لعرض أفضل للنصوص");
                System.out.println("• مساحة كافية للأيقونات والنصوص");
                System.out.println("• تجربة مستخدم محسنة");
                System.out.println("• حفظ تلقائي للعرض المفضل");
                
                // عرض رسالة ترحيبية
                Timer welcomeTimer = new Timer(2000, e -> {
                    JOptionPane.showMessageDialog(mainWindow, 
                        "مرحباً بك في النظام المحسن!\n" +
                        "عرض القائمة الآن: 420 بكسل\n" +
                        "استمتع بتجربة أفضل للتصفح والعمل",
                        "النظام المحسن",
                        JOptionPane.INFORMATION_MESSAGE);
                });
                welcomeTimer.setRepeats(false);
                welcomeTimer.start();
                
            } catch (Exception e) {
                e.printStackTrace();
                UIUtils.showErrorMessage(null, "حدث خطأ في تشغيل التطبيق: " + e.getMessage());
            }
        });
    }
}
