# دليل إعداد قاعدة البيانات - نظام إدارة الشحنات
# Ship ERP Database Setup Guide

## 📋 نظرة عامة

هذا الدليل يوضح كيفية إعداد قاعدة البيانات لنظام إدارة الشحنات باستخدام Oracle Database.

## 🔧 المتطلبات الأساسية

### 1. Oracle Database
- **Oracle Database 12c أو أحدث** (يُفضل 19c أو 21c)
- أو **Oracle Database XE** (النسخة المجانية)
- أو **Oracle Cloud Database**

### 2. Oracle Client Tools
- **Oracle SQL*Plus** (مُضمن مع Oracle Database)
- أو **Oracle Instant Client** مع SQL*Plus

### 3. صلاحيات النظام
- مستخدم بصلاحيات **DBA** (مثل `system` أو `sys`)
- مساحة قرص كافية (على الأقل 2GB)

## 📁 ملفات الإعداد

```
📦 Ship ERP Database Setup
├── 📄 setup-database-enhanced.bat     # ملف إعداد قاعدة البيانات الرئيسي
├── 📄 test-database-connection.bat    # اختبار اتصال قاعدة البيانات
├── 📄 run-ship-erp.bat               # تشغيل التطبيق
├── 📁 scripts/
│   ├── 📄 setup-database.sql         # السكريبت الأساسي
│   └── 📄 setup-database-enhanced.sql # السكريبت المحسن
└── 📄 database-config.properties     # ملف الإعدادات (يُنشأ تلقائياً)
```

## 🚀 خطوات الإعداد

### الخطوة 1: تحضير البيئة

1. **تأكد من تشغيل Oracle Database:**
   ```cmd
   # Windows
   net start OracleServiceORCL
   net start OracleOraDB19Home1TNSListener
   
   # أو من Oracle Services
   services.msc
   ```

2. **اختبار SQL*Plus:**
   ```cmd
   sqlplus -v
   ```

### الخطوة 2: تشغيل إعداد قاعدة البيانات

1. **تشغيل ملف الإعداد:**
   ```cmd
   setup-database-enhanced.bat
   ```

2. **إدخال معلومات الاتصال:**
   - عنوان الخادم: `localhost` (افتراضي)
   - رقم المنفذ: `1521` (افتراضي)
   - اسم قاعدة البيانات: `orcl` (افتراضي)
   - مستخدم DBA: `system` (افتراضي)
   - كلمة مرور DBA: [أدخل كلمة المرور]

3. **تأكيد الإعداد:**
   - اضغط `y` للمتابعة
   - انتظر حتى انتهاء العملية (قد تستغرق عدة دقائق)

### الخطوة 3: التحقق من الإعداد

1. **اختبار الاتصال:**
   ```cmd
   test-database-connection.bat
   ```

2. **التحقق من النتائج:**
   - يجب أن تظهر معلومات قاعدة البيانات
   - عدد الجداول والبيانات المُنشأة
   - حالة المتسلسلات والفهارس

## 📊 هيكل قاعدة البيانات

### الجداول الرئيسية

| الجدول | الوصف | عدد الأعمدة |
|--------|--------|-------------|
| `roles` | أدوار المستخدمين | 9 |
| `users` | المستخدمين | 17 |
| `branches` | الفروع | 15 |
| `currencies` | العملات | 11 |
| `customers` | العملاء | 19 |
| `shipments` | الشحنات | 32 |
| `shipment_tracking` | تتبع الشحنات | 9 |
| `audit_logs` | سجل التدقيق | 12 |
| `system_settings` | إعدادات النظام | 9 |

### البيانات الافتراضية

#### الأدوار (5 أدوار):
- `SUPER_ADMIN` - مدير النظام الرئيسي
- `ADMIN` - مدير النظام
- `MANAGER` - مدير الفرع
- `EMPLOYEE` - موظف
- `VIEWER` - مستعرض

#### العملات (4 عملات):
- `SAR` - ريال سعودي (افتراضي)
- `USD` - دولار أمريكي
- `EUR` - يورو
- `AED` - درهم إماراتي

#### الفروع (3 فروع):
- `MAIN` - الفرع الرئيسي (الرياض)
- `JED` - فرع جدة
- `DAM` - فرع الدمام

#### المستخدمين (2 مستخدمين):
- `admin` / `admin123` - مدير النظام
- `manager` / `manager123` - مدير العمليات

## 🔐 معلومات الاتصال

بعد الإعداد الناجح:

```properties
المستخدم: ship_erp
كلمة المرور: ship_erp_password
قاعدة البيانات: localhost:1521:orcl
Tablespace: SHIP_ERP_DATA
Index Tablespace: SHIP_ERP_INDEX
```

## 🏃‍♂️ تشغيل التطبيق

بعد إعداد قاعدة البيانات:

```cmd
# تشغيل التطبيق
run-ship-erp.bat

# أو تشغيل مباشر
java -jar target/ship-erp-system-1.0.0.jar
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ "Oracle SQL*Plus غير موجود"
**الحل:**
- تثبيت Oracle Database أو Oracle Instant Client
- إضافة مجلد `bin` إلى متغير `PATH`

#### 2. خطأ "ORA-12541: TNS:no listener"
**الحل:**
- تشغيل Oracle TNS Listener:
  ```cmd
  lsnrctl start
  ```

#### 3. خطأ "ORA-01017: invalid username/password"
**الحل:**
- التأكد من كلمة مرور مستخدم DBA
- إعادة تعيين كلمة المرور:
  ```sql
  ALTER USER system IDENTIFIED BY new_password;
  ```

#### 4. خطأ "ORA-01950: no privileges on tablespace"
**الحل:**
- منح صلاحيات إضافية:
  ```sql
  GRANT UNLIMITED TABLESPACE TO ship_erp;
  ```

#### 5. خطأ "Tablespace already exists"
**الحل:**
- حذف Tablespace الموجود:
  ```sql
  DROP TABLESPACE SHIP_ERP_DATA INCLUDING CONTENTS AND DATAFILES;
  ```

### ملفات السجل

- **ملف السجل الرئيسي:** `database-setup.log`
- **ملف الأخطاء:** `database-setup-error.log`
- **سجل التطبيق:** `logs/ship-erp.log`

## 📞 الدعم الفني

للحصول على المساعدة:

1. **مراجعة ملفات السجل** للحصول على تفاصيل الخطأ
2. **التأكد من المتطلبات الأساسية**
3. **اتباع خطوات استكشاف الأخطاء**
4. **التواصل مع فريق الدعم الفني**

## 📝 ملاحظات مهمة

- ⚠️ **تأكد من عمل نسخة احتياطية** قبل تشغيل السكريبت
- ⚠️ **لا تشغل السكريبت على قاعدة بيانات إنتاج** بدون اختبار
- ⚠️ **احتفظ بكلمات المرور في مكان آمن**
- ✅ **اختبر الاتصال** قبل تشغيل التطبيق
- ✅ **راجع ملفات السجل** للتأكد من نجاح العملية

## 🔄 تحديث قاعدة البيانات

لتحديث قاعدة البيانات في المستقبل:

1. **عمل نسخة احتياطية:**
   ```cmd
   exp ship_erp/ship_erp_password file=backup.dmp
   ```

2. **تشغيل سكريبت التحديث:**
   ```cmd
   sqlplus ship_erp/ship_erp_password @scripts/update-database.sql
   ```

3. **اختبار التحديث:**
   ```cmd
   test-database-connection.bat
   ```

---

**تم إعداد هذا الدليل لنظام إدارة الشحنات Ship ERP v1.0.0**
