import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * تطبيق JavaFX بسيط للاختبار
 */
public class SimpleJavaFXApp extends Application {

    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("اختبار تطبيق الشحن");

        Label label = new Label("مرحباً بك في نظام إدارة الشحنات");
        label.setStyle("-fx-font-size: 18px; -fx-font-weight: bold;");

        Button button = new Button("اختبار");
        button.setOnAction(e -> {
            label.setText("تم الضغط على الزر بنجاح!");
        });

        VBox vbox = new VBox(10);
        vbox.setPadding(new Insets(20));
        vbox.getChildren().addAll(label, button);

        Scene scene = new Scene(vbox, 400, 200);
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    public static void main(String[] args) {
        launch(args);
    }
}
