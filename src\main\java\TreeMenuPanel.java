import javax.swing.*;
import javax.swing.tree.*;
import java.awt.*;
import java.awt.event.*;
import java.util.*;
import javax.swing.border.EmptyBorder;

/**
 * لوحة القائمة الشجرية المتقدمة
 * Advanced Tree Menu Panel
 */
public class TreeMenuPanel extends JPanel {
    
    private JTree menuTree;
    private DefaultMutableTreeNode rootNode;
    private DefaultTreeModel treeModel;
    private Font arabicFont;
    private JFrame parentFrame;
    private Map<String, Runnable> menuActions;
    
    public TreeMenuPanel(JFrame parentFrame) {
        this.parentFrame = parentFrame;
        this.arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        this.menuActions = new HashMap<>();
        
        initializeMenuActions();
        createTreeStructure();
        setupTreeComponents();
        setupLayout();
        setupEventHandlers();
    }
    
    /**
     * تهيئة إجراءات القائمة
     */
    private void initializeMenuActions() {
        // إجراءات إدارة الشحنات
        menuActions.put("قائمة الشحنات", () -> showMessage("فتح قائمة الشحنات"));
        menuActions.put("شحنة جديدة", () -> showMessage("إنشاء شحنة جديدة"));
        menuActions.put("تتبع الشحنات", () -> showMessage("فتح نظام تتبع الشحنات"));
        menuActions.put("تقارير الشحن", () -> showMessage("عرض تقارير الشحن"));
        
        // إجراءات إدارة العملاء
        menuActions.put("قائمة العملاء", () -> showMessage("فتح قائمة العملاء"));
        menuActions.put("عميل جديد", () -> showMessage("إضافة عميل جديد"));
        menuActions.put("تقارير العملاء", () -> showMessage("عرض تقارير العملاء"));
        
        // إجراءات المحاسبة
        menuActions.put("الفواتير", () -> showMessage("فتح نظام الفواتير"));
        menuActions.put("المدفوعات", () -> showMessage("إدارة المدفوعات"));
        menuActions.put("التقارير المالية", () -> showMessage("عرض التقارير المالية"));
        menuActions.put("الحسابات", () -> showMessage("إدارة الحسابات"));
        
        // إجراءات المخزون
        menuActions.put("قائمة المخزون", () -> showMessage("فتح قائمة المخزون"));
        menuActions.put("إضافة عنصر", () -> showMessage("إضافة عنصر جديد للمخزون"));
        menuActions.put("جرد المخزون", () -> showMessage("تنفيذ جرد المخزون"));
        menuActions.put("تقارير المخزون", () -> showMessage("عرض تقارير المخزون"));
        
        // إجراءات التقارير
        menuActions.put("تقارير المبيعات", () -> showMessage("عرض تقارير المبيعات"));
        menuActions.put("تقارير الأرباح", () -> showMessage("عرض تقارير الأرباح"));
        menuActions.put("تقارير الأداء", () -> showMessage("عرض تقارير الأداء"));
        menuActions.put("تقارير مخصصة", () -> showMessage("إنشاء تقارير مخصصة"));
        
        // إجراءات الإعدادات
        menuActions.put("الإعدادات العامة", () -> {
            GeneralSettingsWindow settingsWindow = new GeneralSettingsWindow(parentFrame);
            settingsWindow.setVisible(true);
        });
        menuActions.put("إدارة المستخدمين", () -> {
            UserManagementWindow usersWindow = new UserManagementWindow(parentFrame);
            usersWindow.setVisible(true);
        });
        menuActions.put("إعدادات النظام", () -> showMessage("فتح إعدادات النظام"));
        menuActions.put("النسخ الاحتياطية", () -> showMessage("إدارة النسخ الاحتياطية"));
        
        // إجراءات المساعدة
        menuActions.put("دليل المستخدم", () -> showMessage("فتح دليل المستخدم"));
        menuActions.put("الدعم الفني", () -> showMessage("الاتصال بالدعم الفني"));
        menuActions.put("حول البرنامج", () -> showAboutDialog());
    }
    
    /**
     * إنشاء هيكل الشجرة
     */
    private void createTreeStructure() {
        rootNode = new DefaultMutableTreeNode("نظام إدارة الشحنات");
        
        // إدارة الشحنات
        DefaultMutableTreeNode shippingNode = new DefaultMutableTreeNode("إدارة الشحنات");
        shippingNode.add(new DefaultMutableTreeNode("قائمة الشحنات"));
        shippingNode.add(new DefaultMutableTreeNode("شحنة جديدة"));
        shippingNode.add(new DefaultMutableTreeNode("تتبع الشحنات"));
        shippingNode.add(new DefaultMutableTreeNode("تقارير الشحن"));
        rootNode.add(shippingNode);
        
        // إدارة العملاء
        DefaultMutableTreeNode customersNode = new DefaultMutableTreeNode("إدارة العملاء");
        customersNode.add(new DefaultMutableTreeNode("قائمة العملاء"));
        customersNode.add(new DefaultMutableTreeNode("عميل جديد"));
        customersNode.add(new DefaultMutableTreeNode("تقارير العملاء"));
        rootNode.add(customersNode);
        
        // المحاسبة والمالية
        DefaultMutableTreeNode accountingNode = new DefaultMutableTreeNode("المحاسبة والمالية");
        accountingNode.add(new DefaultMutableTreeNode("الفواتير"));
        accountingNode.add(new DefaultMutableTreeNode("المدفوعات"));
        accountingNode.add(new DefaultMutableTreeNode("التقارير المالية"));
        accountingNode.add(new DefaultMutableTreeNode("الحسابات"));
        rootNode.add(accountingNode);
        
        // إدارة المخزون
        DefaultMutableTreeNode inventoryNode = new DefaultMutableTreeNode("إدارة المخزون");
        inventoryNode.add(new DefaultMutableTreeNode("قائمة المخزون"));
        inventoryNode.add(new DefaultMutableTreeNode("إضافة عنصر"));
        inventoryNode.add(new DefaultMutableTreeNode("جرد المخزون"));
        inventoryNode.add(new DefaultMutableTreeNode("تقارير المخزون"));
        rootNode.add(inventoryNode);
        
        // التقارير والإحصائيات
        DefaultMutableTreeNode reportsNode = new DefaultMutableTreeNode("التقارير والإحصائيات");
        reportsNode.add(new DefaultMutableTreeNode("تقارير المبيعات"));
        reportsNode.add(new DefaultMutableTreeNode("تقارير الأرباح"));
        reportsNode.add(new DefaultMutableTreeNode("تقارير الأداء"));
        reportsNode.add(new DefaultMutableTreeNode("تقارير مخصصة"));
        rootNode.add(reportsNode);
        
        // الإعدادات والإدارة
        DefaultMutableTreeNode settingsNode = new DefaultMutableTreeNode("الإعدادات والإدارة");
        settingsNode.add(new DefaultMutableTreeNode("الإعدادات العامة"));
        settingsNode.add(new DefaultMutableTreeNode("إدارة المستخدمين"));
        settingsNode.add(new DefaultMutableTreeNode("إعدادات النظام"));
        settingsNode.add(new DefaultMutableTreeNode("النسخ الاحتياطية"));
        rootNode.add(settingsNode);
        
        // المساعدة والدعم
        DefaultMutableTreeNode helpNode = new DefaultMutableTreeNode("المساعدة والدعم");
        helpNode.add(new DefaultMutableTreeNode("دليل المستخدم"));
        helpNode.add(new DefaultMutableTreeNode("الدعم الفني"));
        helpNode.add(new DefaultMutableTreeNode("حول البرنامج"));
        rootNode.add(helpNode);
        
        treeModel = new DefaultTreeModel(rootNode);
    }
    
    /**
     * إعداد مكونات الشجرة
     */
    private void setupTreeComponents() {
        menuTree = new JTree(treeModel);
        menuTree.setFont(arabicFont);
        menuTree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        menuTree.setRootVisible(true);
        menuTree.setShowsRootHandles(true);
        menuTree.setRowHeight(25);
        
        // تخصيص مظهر الشجرة
        menuTree.setCellRenderer(new CustomTreeCellRenderer());
        
        // توسيع العقد الرئيسية
        for (int i = 0; i < menuTree.getRowCount(); i++) {
            menuTree.expandRow(i);
        }
        
        // تحديد العقدة الجذر
        menuTree.setSelectionRow(0);
    }
    
    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setBackground(new Color(248, 249, 250));
        setBorder(new EmptyBorder(10, 10, 10, 10));
        
        // رأس القائمة
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);
        
        // الشجرة مع شريط التمرير
        JScrollPane scrollPane = new JScrollPane(menuTree);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createTitledBorder("القائمة الرئيسية"),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));
        scrollPane.setPreferredSize(new Dimension(400, 500)); // تحسين الحجم ليناسب 420 بكسل
        scrollPane.setMinimumSize(new Dimension(350, 400)); // حد أدنى محسن للحجم
        
        add(scrollPane, BorderLayout.CENTER);
        
        // تذييل مع معلومات المستخدم
        JPanel footerPanel = createFooterPanel();
        add(footerPanel, BorderLayout.SOUTH);
    }
    
    /**
     * إنشاء رأس القائمة
     */
    private JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(52, 152, 219));
        panel.setBorder(new EmptyBorder(15, 15, 15, 15));
        
        JLabel titleLabel = new JLabel("القائمة الرئيسية");
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        panel.add(titleLabel, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * إنشاء تذييل القائمة
     */
    private JPanel createFooterPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(248, 249, 250));
        panel.setBorder(new EmptyBorder(10, 15, 10, 15));
        
        JLabel userLabel = new JLabel("المستخدم: admin");
        userLabel.setFont(new Font("Tahoma", Font.PLAIN, 11));
        userLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel timeLabel = new JLabel("الوقت: " + new java.text.SimpleDateFormat("HH:mm").format(new java.util.Date()));
        timeLabel.setFont(new Font("Tahoma", Font.PLAIN, 11));
        timeLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        panel.add(userLabel, BorderLayout.NORTH);
        panel.add(timeLabel, BorderLayout.SOUTH);
        
        return panel;
    }

    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        menuTree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode = (DefaultMutableTreeNode) menuTree.getLastSelectedPathComponent();

            if (selectedNode != null && selectedNode.isLeaf() && !selectedNode.isRoot()) {
                String menuItem = selectedNode.toString();
                Runnable action = menuActions.get(menuItem);

                if (action != null) {
                    action.run();
                }
            }
        });

        // إضافة قائمة سياق (Right-click menu)
        menuTree.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (SwingUtilities.isRightMouseButton(e)) {
                    showContextMenu(e);
                }
            }
        });
    }

    /**
     * عرض قائمة السياق
     */
    private void showContextMenu(MouseEvent e) {
        TreePath path = menuTree.getPathForLocation(e.getX(), e.getY());
        if (path != null) {
            menuTree.setSelectionPath(path);
            DefaultMutableTreeNode node = (DefaultMutableTreeNode) path.getLastPathComponent();

            JPopupMenu contextMenu = new JPopupMenu();
            contextMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

            if (node.isLeaf() && !node.isRoot()) {
                JMenuItem openItem = new JMenuItem("فتح");
                openItem.setFont(arabicFont);
                openItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                openItem.addActionListener(ev -> {
                    String menuItem = node.toString();
                    Runnable action = menuActions.get(menuItem);
                    if (action != null) {
                        action.run();
                    }
                });
                contextMenu.add(openItem);

                contextMenu.addSeparator();

                JMenuItem infoItem = new JMenuItem("معلومات");
                infoItem.setFont(arabicFont);
                infoItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                infoItem.addActionListener(ev -> showItemInfo(node.toString()));
                contextMenu.add(infoItem);
            } else {
                JMenuItem expandItem = new JMenuItem(menuTree.isExpanded(path) ? "طي" : "توسيع");
                expandItem.setFont(arabicFont);
                expandItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                expandItem.addActionListener(ev -> {
                    if (menuTree.isExpanded(path)) {
                        menuTree.collapsePath(path);
                    } else {
                        menuTree.expandPath(path);
                    }
                });
                contextMenu.add(expandItem);
            }

            contextMenu.show(menuTree, e.getX(), e.getY());
        }
    }

    /**
     * عرض معلومات العنصر
     */
    private void showItemInfo(String itemName) {
        String info = getItemDescription(itemName);
        JOptionPane.showMessageDialog(this, info, "معلومات: " + itemName, JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * الحصول على وصف العنصر
     */
    private String getItemDescription(String itemName) {
        Map<String, String> descriptions = new HashMap<>();

        descriptions.put("قائمة الشحنات", "عرض وإدارة جميع الشحنات في النظام");
        descriptions.put("شحنة جديدة", "إنشاء شحنة جديدة وإدخال بياناتها");
        descriptions.put("تتبع الشحنات", "تتبع حالة الشحنات ومعرفة موقعها");
        descriptions.put("تقارير الشحن", "عرض تقارير مفصلة عن عمليات الشحن");

        descriptions.put("قائمة العملاء", "عرض وإدارة بيانات جميع العملاء");
        descriptions.put("عميل جديد", "إضافة عميل جديد إلى النظام");
        descriptions.put("تقارير العملاء", "عرض تقارير عن نشاط العملاء");

        descriptions.put("الفواتير", "إدارة الفواتير وإنشاء فواتير جديدة");
        descriptions.put("المدفوعات", "تسجيل ومتابعة المدفوعات");
        descriptions.put("التقارير المالية", "عرض التقارير المالية والمحاسبية");
        descriptions.put("الحسابات", "إدارة الحسابات المالية");

        descriptions.put("الإعدادات العامة", "تكوين الإعدادات العامة للنظام");
        descriptions.put("إدارة المستخدمين", "إدارة المستخدمين وصلاحياتهم");

        return descriptions.getOrDefault(itemName, "لا توجد معلومات متاحة لهذا العنصر");
    }

    /**
     * عرض رسالة
     */
    private void showMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "إشعار", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * عرض نافذة حول البرنامج
     */
    private void showAboutDialog() {
        String aboutText = "نظام إدارة الشحنات المتقدم\n" +
                          "الإصدار: 1.0\n" +
                          "تطوير: فريق التطوير\n" +
                          "حقوق الطبع محفوظة © 2024";

        JOptionPane.showMessageDialog(this, aboutText, "حول البرنامج", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * فئة مخصصة لعرض خلايا الشجرة
     */
    private class CustomTreeCellRenderer extends DefaultTreeCellRenderer {

        public CustomTreeCellRenderer() {
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        }

        @Override
        public Component getTreeCellRendererComponent(JTree tree, Object value,
                boolean selected, boolean expanded, boolean leaf, int row, boolean hasFocus) {

            super.getTreeCellRendererComponent(tree, value, selected, expanded, leaf, row, hasFocus);

            setFont(arabicFont);
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            setHorizontalAlignment(SwingConstants.RIGHT);

            // تخصيص الأيقونات
            DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
            String nodeText = node.toString();

            if (node.isRoot()) {
                setIcon(createColoredIcon(new Color(52, 152, 219)));
            } else if (!leaf) {
                // أيقونات المجلدات الرئيسية
                if (nodeText.contains("الشحنات")) {
                    setIcon(createColoredIcon(new Color(40, 167, 69)));
                } else if (nodeText.contains("العملاء")) {
                    setIcon(createColoredIcon(new Color(255, 193, 7)));
                } else if (nodeText.contains("المحاسبة")) {
                    setIcon(createColoredIcon(new Color(220, 53, 69)));
                } else if (nodeText.contains("المخزون")) {
                    setIcon(createColoredIcon(new Color(111, 66, 193)));
                } else if (nodeText.contains("التقارير")) {
                    setIcon(createColoredIcon(new Color(23, 162, 184)));
                } else if (nodeText.contains("الإعدادات")) {
                    setIcon(createColoredIcon(new Color(108, 117, 125)));
                } else if (nodeText.contains("المساعدة")) {
                    setIcon(createColoredIcon(new Color(255, 87, 34)));
                } else {
                    setIcon(createColoredIcon(new Color(76, 175, 80)));
                }
            } else {
                // أيقونات العناصر الفرعية
                setIcon(createColoredIcon(new Color(158, 158, 158)));
            }

            // تخصيص الألوان
            if (selected) {
                setBackgroundSelectionColor(new Color(52, 152, 219, 100));
                setTextSelectionColor(Color.BLACK);
            } else {
                setBackgroundNonSelectionColor(Color.WHITE);
                setTextNonSelectionColor(Color.BLACK);
            }

            return this;
        }

        /**
         * إنشاء أيقونة ملونة
         */
        private Icon createColoredIcon(Color color) {
            return new Icon() {
                @Override
                public void paintIcon(Component c, Graphics g, int x, int y) {
                    Graphics2D g2d = (Graphics2D) g.create();
                    g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    g2d.setColor(color);
                    g2d.fillOval(x + 2, y + 2, getIconWidth() - 4, getIconHeight() - 4);
                    g2d.setColor(color.darker());
                    g2d.drawOval(x + 2, y + 2, getIconWidth() - 4, getIconHeight() - 4);
                    g2d.dispose();
                }

                @Override
                public int getIconWidth() {
                    return 16;
                }

                @Override
                public int getIconHeight() {
                    return 16;
                }
            };
        }
    }

    /**
     * توسيع جميع العقد
     */
    public void expandAllNodes() {
        for (int i = 0; i < menuTree.getRowCount(); i++) {
            menuTree.expandRow(i);
        }
    }

    /**
     * طي جميع العقد
     */
    public void collapseAllNodes() {
        for (int i = menuTree.getRowCount() - 1; i >= 1; i--) {
            menuTree.collapseRow(i);
        }
    }

    /**
     * البحث في القائمة
     */
    public void searchInMenu(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            expandAllNodes();
            return;
        }

        collapseAllNodes();
        searchAndExpand(rootNode, searchText.toLowerCase());
    }

    /**
     * البحث وتوسيع العقد المطابقة
     */
    private boolean searchAndExpand(DefaultMutableTreeNode node, String searchText) {
        boolean found = false;

        if (node.toString().toLowerCase().contains(searchText)) {
            TreePath path = new TreePath(node.getPath());
            menuTree.expandPath(path);
            found = true;
        }

        for (int i = 0; i < node.getChildCount(); i++) {
            DefaultMutableTreeNode child = (DefaultMutableTreeNode) node.getChildAt(i);
            if (searchAndExpand(child, searchText)) {
                TreePath path = new TreePath(node.getPath());
                menuTree.expandPath(path);
                found = true;
            }
        }

        return found;
    }
}
