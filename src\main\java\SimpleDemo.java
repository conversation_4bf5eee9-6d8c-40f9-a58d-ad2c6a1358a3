import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * عرض توضيحي مبسط لنظام إدارة الشحنات
 * Simple demo for Ship ERP System
 */
public class SimpleDemo {
    
    private JFrame loginFrame;
    private JFrame mainFrame;
    private JTextField usernameField;
    private JPasswordField passwordField;
    private JLabel statusLabel;
    private Timer clockTimer;
    
    public static void main(String[] args) {
        // تعيين الترميز والمحلية العربية
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("user.language", "ar");
        System.setProperty("user.country", "SA");

        // تعيين اتجاه النص من اليمين لليسار
        System.setProperty("swing.defaultlaf", UIManager.getSystemLookAndFeelClassName());

        SwingUtilities.invokeLater(() -> {
            new SimpleDemo().showLoginScreen();
        });
    }
    
    /**
     * عرض شاشة تسجيل الدخول
     */
    private void showLoginScreen() {
        loginFrame = new JFrame("نظام إدارة الشحنات - تسجيل الدخول");
        loginFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        loginFrame.setSize(400, 300);
        loginFrame.setLocationRelativeTo(null);
        loginFrame.setResizable(false);
        
        // تعيين الخط العربي
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 14);
        
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        // العنوان
        JLabel titleLabel = new JLabel("نظام إدارة الشحنات", SwingConstants.CENTER);
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 18));
        titleLabel.setForeground(new Color(0, 123, 255));
        mainPanel.add(titleLabel, BorderLayout.NORTH);
        
        // نموذج تسجيل الدخول
        JPanel formPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // اسم المستخدم
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.EAST;
        JLabel userLabel = new JLabel("اسم المستخدم:");
        userLabel.setFont(arabicFont);
        formPanel.add(userLabel, gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        usernameField = new JTextField("admin", 15);
        usernameField.setFont(arabicFont);
        formPanel.add(usernameField, gbc);
        
        // كلمة المرور
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        JLabel passLabel = new JLabel("كلمة المرور:");
        passLabel.setFont(arabicFont);
        formPanel.add(passLabel, gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        passwordField = new JPasswordField("admin123", 15);
        passwordField.setFont(arabicFont);
        formPanel.add(passwordField, gbc);
        
        // أزرار
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.NONE;
        JPanel buttonPanel = new JPanel(new FlowLayout());
        
        JButton loginButton = new JButton("تسجيل الدخول");
        loginButton.setFont(arabicFont);
        loginButton.setBackground(new Color(0, 123, 255));
        loginButton.setForeground(Color.WHITE);
        loginButton.addActionListener(new LoginActionListener());
        
        JButton cancelButton = new JButton("إلغاء");
        cancelButton.setFont(arabicFont);
        cancelButton.addActionListener(e -> System.exit(0));
        
        buttonPanel.add(loginButton);
        buttonPanel.add(cancelButton);
        formPanel.add(buttonPanel, gbc);
        
        mainPanel.add(formPanel, BorderLayout.CENTER);
        
        // شريط الحالة
        statusLabel = new JLabel("جاهز", SwingConstants.CENTER);
        statusLabel.setFont(arabicFont);
        statusLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        mainPanel.add(statusLabel, BorderLayout.SOUTH);
        
        loginFrame.add(mainPanel);
        loginFrame.setVisible(true);
        
        // تركيز على حقل اسم المستخدم
        usernameField.requestFocus();
        
        // ربط Enter بتسجيل الدخول
        passwordField.addActionListener(new LoginActionListener());
    }
    
    /**
     * عرض الواجهة الرئيسية
     */
    private void showMainInterface() {
        loginFrame.dispose();
        
        mainFrame = new JFrame("نظام إدارة الشحنات - الواجهة الرئيسية");
        mainFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        mainFrame.setSize(1000, 700);
        mainFrame.setLocationRelativeTo(null);
        mainFrame.setExtendedState(JFrame.MAXIMIZED_BOTH);
        
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        // شريط القوائم
        JMenuBar menuBar = new JMenuBar();
        
        // قائمة الملف
        JMenu fileMenu = new JMenu("ملف");
        fileMenu.setFont(arabicFont);
        fileMenu.add(createMenuItem("جديد", arabicFont));
        fileMenu.add(createMenuItem("فتح", arabicFont));
        fileMenu.addSeparator();
        fileMenu.add(createMenuItem("حفظ", arabicFont));
        fileMenu.add(createMenuItem("طباعة", arabicFont));
        fileMenu.addSeparator();
        JMenuItem exitItem = createMenuItem("خروج", arabicFont);
        exitItem.addActionListener(e -> System.exit(0));
        fileMenu.add(exitItem);
        
        // قائمة الإعدادات
        JMenu settingsMenu = new JMenu("الإعدادات");
        settingsMenu.setFont(arabicFont);
        settingsMenu.add(createMenuItem("المتغيرات العامة", arabicFont));
        settingsMenu.add(createMenuItem("المستخدمين", arabicFont));
        settingsMenu.add(createMenuItem("العملات", arabicFont));
        settingsMenu.add(createMenuItem("بيانات الشركة", arabicFont));
        
        // قائمة المساعدة
        JMenu helpMenu = new JMenu("مساعدة");
        helpMenu.setFont(arabicFont);
        JMenuItem aboutItem = createMenuItem("حول البرنامج", arabicFont);
        aboutItem.addActionListener(e -> showAboutDialog());
        helpMenu.add(aboutItem);
        
        menuBar.add(fileMenu);
        menuBar.add(settingsMenu);
        menuBar.add(helpMenu);
        mainFrame.setJMenuBar(menuBar);
        
        // المحتوى الرئيسي
        JPanel mainPanel = new JPanel(new BorderLayout());
        
        // الشريط الجانبي
        JPanel sidePanel = new JPanel(new BorderLayout());
        sidePanel.setPreferredSize(new Dimension(250, 0));
        sidePanel.setBorder(BorderFactory.createTitledBorder("القائمة الرئيسية"));
        
        String[] menuItems = {
            "الإعدادات العامة",
            "إدارة المستخدمين", 
            "إدارة العملات",
            "بيانات الشركة",
            "إدارة الأصناف",
            "إدارة الموردين",
            "متابعة الشحنات",
            "الإدخالات الجمركية",
            "إدارة التكاليف",
            "التقارير"
        };
        
        JList<String> menuList = new JList<>(menuItems);
        menuList.setFont(arabicFont);
        menuList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        menuList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                String selected = menuList.getSelectedValue();
                if (selected != null) {
                    showFeatureDialog(selected);
                }
            }
        });
        
        sidePanel.add(new JScrollPane(menuList), BorderLayout.CENTER);
        
        // منطقة المحتوى
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBorder(BorderFactory.createTitledBorder("المحتوى"));
        
        // رسالة الترحيب
        JPanel welcomePanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        
        JLabel welcomeLabel = new JLabel("مرحباً بك في نظام إدارة الشحنات");
        welcomeLabel.setFont(new Font("Tahoma", Font.BOLD, 24));
        welcomeLabel.setForeground(new Color(0, 123, 255));
        gbc.gridx = 0; gbc.gridy = 0; gbc.insets = new Insets(20, 20, 20, 20);
        welcomePanel.add(welcomeLabel, gbc);
        
        JTextArea infoArea = new JTextArea(
            "نظام إدارة شحنات متكامل وشامل ومتقدم\n\n" +
            "الميزات المتاحة:\n" +
            "✓ نظام تسجيل الدخول الآمن\n" +
            "✓ إدارة المستخدمين والصلاحيات\n" +
            "✓ إعدادات الشركة والفروع\n" +
            "✓ إدارة العملات وأسعار الصرف\n" +
            "✓ واجهة عربية كاملة\n" +
            "✓ نظام أمان متقدم\n\n" +
            "قيد التطوير:\n" +
            "• نظام إدارة الأصناف\n" +
            "• نظام إدارة الموردين\n" +
            "• نظام متابعة الشحنات\n" +
            "• نظام الإدخالات الجمركية"
        );
        infoArea.setFont(arabicFont);
        infoArea.setEditable(false);
        infoArea.setOpaque(false);
        gbc.gridy = 1;
        welcomePanel.add(infoArea, gbc);
        
        contentPanel.add(welcomePanel, BorderLayout.CENTER);
        
        mainPanel.add(sidePanel, BorderLayout.WEST);
        mainPanel.add(contentPanel, BorderLayout.CENTER);
        
        // شريط الحالة
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        JLabel statusLeft = new JLabel("جاهز");
        statusLeft.setFont(arabicFont);
        
        JLabel statusRight = new JLabel();
        statusRight.setFont(arabicFont);
        statusRight.setHorizontalAlignment(SwingConstants.RIGHT);
        
        statusPanel.add(statusLeft, BorderLayout.WEST);
        statusPanel.add(statusRight, BorderLayout.EAST);
        
        // ساعة رقمية
        clockTimer = new Timer(1000, e -> {
            LocalDateTime now = LocalDateTime.now();
            String timeText = now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd - HH:mm:ss"));
            statusRight.setText("المستخدم: admin | " + timeText);
        });
        clockTimer.start();
        
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
        
        mainFrame.add(mainPanel);
        mainFrame.setVisible(true);
    }
    
    private JMenuItem createMenuItem(String text, Font font) {
        JMenuItem item = new JMenuItem(text);
        item.setFont(font);
        return item;
    }
    
    private void showFeatureDialog(String feature) {
        JOptionPane.showMessageDialog(
            mainFrame,
            "الميزة '" + feature + "' قيد التطوير\nستكون متاحة في الإصدارات القادمة",
            "قيد التطوير",
            JOptionPane.INFORMATION_MESSAGE
        );
    }
    
    private void showAboutDialog() {
        String aboutText = 
            "نظام إدارة الشحنات\n" +
            "Ship ERP System\n\n" +
            "الإصدار: 1.0.0\n" +
            "التقنيات: Java + JavaFX + Spring + Oracle\n\n" +
            "نظام متكامل لإدارة الشحن والاستيراد\n\n" +
            "جميع الحقوق محفوظة © 2025";
            
        JOptionPane.showMessageDialog(
            mainFrame,
            aboutText,
            "حول البرنامج",
            JOptionPane.INFORMATION_MESSAGE
        );
    }
    
    /**
     * معالج تسجيل الدخول
     */
    private class LoginActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String username = usernameField.getText().trim();
            String password = new String(passwordField.getPassword());
            
            if (username.isEmpty()) {
                JOptionPane.showMessageDialog(loginFrame, "اسم المستخدم مطلوب", "خطأ", JOptionPane.ERROR_MESSAGE);
                usernameField.requestFocus();
                return;
            }
            
            if (password.isEmpty()) {
                JOptionPane.showMessageDialog(loginFrame, "كلمة المرور مطلوبة", "خطأ", JOptionPane.ERROR_MESSAGE);
                passwordField.requestFocus();
                return;
            }
            
            statusLabel.setText("جاري تسجيل الدخول...");
            
            // محاكاة تسجيل الدخول
            Timer loginTimer = new Timer(1500, event -> {
                if ("admin".equals(username) && "admin123".equals(password)) {
                    showMainInterface();
                } else {
                    statusLabel.setText("جاهز");
                    JOptionPane.showMessageDialog(
                        loginFrame, 
                        "اسم المستخدم أو كلمة المرور غير صحيحة", 
                        "خطأ في تسجيل الدخول", 
                        JOptionPane.ERROR_MESSAGE
                    );
                    passwordField.selectAll();
                    passwordField.requestFocus();
                }
            });
            loginTimer.setRepeats(false);
            loginTimer.start();
        }
    }
}
