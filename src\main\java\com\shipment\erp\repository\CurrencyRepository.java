package com.shipment.erp.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.shipment.erp.model.Currency;

/**
 * Repository للعملات
 */
@Repository
public interface CurrencyRepository extends JpaRepository<Currency, Long> {

    /**
     * البحث عن عملة بالرمز
     */
    Optional<Currency> findByCode(String code);

    /**
     * البحث عن عملة بالاسم
     */
    Optional<Currency> findByName(String name);

    /**
     * البحث عن العملة الافتراضية
     */
    Optional<Currency> findByIsDefaultTrue();

    /**
     * البحث عن العملات النشطة
     */
    List<Currency> findByActiveTrue();

    /**
     * البحث عن العملات غير النشطة
     */
    List<Currency> findInactiveCurrencies();

    /**
     * البحث عن العملات بالاسم (جزئي)
     */
    List<Currency> findByNameContaining(String name);

    /**
     * التحقق من وجود عملة بالرمز
     */
    boolean existsByCode(String code);

    /**
     * التحقق من وجود عملة بالاسم
     */
    boolean existsByName(String name);

    /**
     * التحقق من وجود عملة افتراضية
     */
    boolean hasDefaultCurrency();

    /**
     * الحصول على عدد العملات النشطة
     */
    long countActiveCurrencies();

    /**
     * الحصول على عدد العملات غير النشطة
     */
    long countInactiveCurrencies();

    /**
     * تعيين عملة كافتراضية
     */
    void setAsDefault(Long currencyId);

    /**
     * إلغاء تعيين العملة الافتراضية
     */
    void unsetDefault();

    /**
     * الحصول على العملات مرتبة بالاستخدام
     */
    List<Currency> findMostUsedCurrencies(int limit);

    /**
     * البحث المتقدم في العملات
     */
    List<Currency> advancedSearch(String code, String name, Boolean isActive, Boolean isDefault);
}
