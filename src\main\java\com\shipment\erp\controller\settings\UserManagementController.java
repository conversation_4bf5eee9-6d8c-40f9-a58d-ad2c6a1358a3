package com.shipment.erp.controller.settings;

import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.shipment.erp.ShipERPApplication;
import com.shipment.erp.model.Branch;
import com.shipment.erp.model.Role;
import com.shipment.erp.model.User;
import com.shipment.erp.service.BranchService;
import com.shipment.erp.service.RoleService;
import com.shipment.erp.service.UserService;
import com.shipment.erp.util.DateUtil;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.HBox;
import javafx.util.StringConverter;

/**
 * Controller لإدارة المستخدمين
 */
public class UserManagementController implements Initializable {

    private static final Logger logger = LoggerFactory.getLogger(UserManagementController.class);

    @FXML private TextField searchField;
    @FXML private ComboBox<Role> roleFilterCombo;
    @FXML private ComboBox<String> statusFilterCombo;
    @FXML private Button addUserButton;
    @FXML private Button refreshButton;
    
    @FXML private TableView<User> usersTable;
    @FXML private TableColumn<User, String> usernameColumn;
    @FXML private TableColumn<User, String> fullNameColumn;
    @FXML private TableColumn<User, String> emailColumn;
    @FXML private TableColumn<User, String> roleColumn;
    @FXML private TableColumn<User, String> branchColumn;
    @FXML private TableColumn<User, String> statusColumn;
    @FXML private TableColumn<User, String> lastLoginColumn;
    @FXML private TableColumn<User, Void> actionsColumn;
    
    @FXML private MenuItem editMenuItem;
    @FXML private MenuItem deleteMenuItem;
    @FXML private MenuItem resetPasswordMenuItem;
    @FXML private MenuItem lockUserMenuItem;
    @FXML private MenuItem unlockUserMenuItem;
    
    @FXML private Label statusLabel;
    @FXML private Label recordCountLabel;

    private UserService userService;
    private RoleService roleService;
    private BranchService branchService;
    private ResourceBundle resources;
    
    private ObservableList<User> allUsers = FXCollections.observableArrayList();
    private ObservableList<User> filteredUsers = FXCollections.observableArrayList();

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        this.resources = resources;
        
        try {
            // الحصول على Services من Spring Context
            userService = ShipERPApplication.getBean(UserService.class);
            roleService = ShipERPApplication.getBean(RoleService.class);
            branchService = ShipERPApplication.getBean(BranchService.class);
            
            setupUI();
            loadData();
            
            logger.info("تم تهيئة شاشة إدارة المستخدمين بنجاح");
            
        } catch (Exception e) {
            logger.error("خطأ في تهيئة شاشة إدارة المستخدمين", e);
            showError("خطأ في تهيئة الشاشة: " + e.getMessage());
        }
    }

    /**
     * إعداد واجهة المستخدم
     */
    private void setupUI() {
        setupTable();
        setupFilters();
        setupContextMenu();
    }

    /**
     * إعداد الجدول
     */
    private void setupTable() {
        // إعداد الأعمدة
        usernameColumn.setCellValueFactory(new PropertyValueFactory<>("username"));
        fullNameColumn.setCellValueFactory(new PropertyValueFactory<>("fullName"));
        emailColumn.setCellValueFactory(new PropertyValueFactory<>("email"));
        
        roleColumn.setCellValueFactory(cellData -> {
            Role role = cellData.getValue().getRole();
            return new SimpleStringProperty(role != null ? role.getName() : "");
        });
        
        branchColumn.setCellValueFactory(cellData -> {
            Branch branch = cellData.getValue().getBranch();
            return new SimpleStringProperty(branch != null ? branch.getName() : "");
        });
        
        statusColumn.setCellValueFactory(cellData -> {
            User user = cellData.getValue();
            String status;
            if (user.isLocked()) {
                status = "مقفل";
            } else if (user.getIsActive()) {
                status = "نشط";
            } else {
                status = "غير نشط";
            }
            return new SimpleStringProperty(status);
        });
        
        lastLoginColumn.setCellValueFactory(cellData -> {
            User user = cellData.getValue();
            String lastLogin = user.getLastLogin() != null ? 
                DateUtil.formatDateTimeArabic(user.getLastLogin()) : "لم يسجل دخول";
            return new SimpleStringProperty(lastLogin);
        });
        
        // إعداد عمود الإجراءات
        setupActionsColumn();
        
        // ربط البيانات بالجدول
        usersTable.setItems(filteredUsers);
        
        // معالج تحديد الصف
        usersTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            updateContextMenuState(newSelection);
        });
    }

    /**
     * إعداد عمود الإجراءات
     */
    private void setupActionsColumn() {
        actionsColumn.setCellFactory(param -> new TableCell<User, Void>() {
            private final Button editButton = new Button("تعديل");
            private final Button deleteButton = new Button("حذف");
            private final HBox buttonsBox = new HBox(5, editButton, deleteButton);

            {
                editButton.setOnAction(event -> {
                    User user = getTableView().getItems().get(getIndex());
                    handleEditUser(user);
                });
                
                deleteButton.setOnAction(event -> {
                    User user = getTableView().getItems().get(getIndex());
                    handleDeleteUser(user);
                });
                
                editButton.getStyleClass().add("small-button");
                deleteButton.getStyleClass().add("small-button");
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(buttonsBox);
                }
            }
        });
    }

    /**
     * إعداد الفلاتر
     */
    private void setupFilters() {
        // فلتر الحالة
        statusFilterCombo.getItems().addAll("الكل", "نشط", "غير نشط", "مقفل");
        statusFilterCombo.setValue("الكل");
        
        // تحميل الأدوار
        loadRoles();
    }

    /**
     * إعداد القائمة السياقية
     */
    private void setupContextMenu() {
        // تعطيل العناصر في البداية
        updateContextMenuState(null);
    }

    /**
     * تحميل الأدوار
     */
    private void loadRoles() {
        try {
            List<Role> roles = roleService.findActive();
            roleFilterCombo.getItems().clear();
            roleFilterCombo.getItems().add(null); // خيار "الكل"
            roleFilterCombo.getItems().addAll(roles);
            
            // تخصيص عرض النص
            roleFilterCombo.setConverter(new StringConverter<Role>() {
                @Override
                public String toString(Role role) {
                    return role != null ? role.getName() : "الكل";
                }

                @Override
                public Role fromString(String string) {
                    return null;
                }
            });
            
        } catch (Exception e) {
            logger.error("خطأ في تحميل الأدوار", e);
        }
    }

    /**
     * تحميل البيانات
     */
    private void loadData() {
        try {
            statusLabel.setText("جاري تحميل البيانات...");
            
            List<User> users = userService.findAll();
            allUsers.setAll(users);
            applyFilters();
            
            updateRecordCount();
            statusLabel.setText("جاهز");
            
            logger.info("تم تحميل {} مستخدم", users.size());
            
        } catch (Exception e) {
            logger.error("خطأ في تحميل البيانات", e);
            showError("خطأ في تحميل البيانات: " + e.getMessage());
            statusLabel.setText("خطأ في التحميل");
        }
    }

    /**
     * تطبيق الفلاتر
     */
    private void applyFilters() {
        String searchText = searchField.getText().toLowerCase().trim();
        Role selectedRole = roleFilterCombo.getValue();
        String selectedStatus = statusFilterCombo.getValue();
        
        filteredUsers.clear();
        
        for (User user : allUsers) {
            // فلتر البحث
            if (!searchText.isEmpty()) {
                boolean matches = user.getUsername().toLowerCase().contains(searchText) ||
                                user.getFullName().toLowerCase().contains(searchText) ||
                                (user.getEmail() != null && user.getEmail().toLowerCase().contains(searchText));
                if (!matches) continue;
            }
            
            // فلتر الدور
            if (selectedRole != null && !selectedRole.equals(user.getRole())) {
                continue;
            }
            
            // فلتر الحالة
            if (!"الكل".equals(selectedStatus)) {
                boolean matches = false;
                if ("نشط".equals(selectedStatus) && user.getIsActive() && !user.isLocked()) {
                    matches = true;
                } else if ("غير نشط".equals(selectedStatus) && !user.getIsActive()) {
                    matches = true;
                } else if ("مقفل".equals(selectedStatus) && user.isLocked()) {
                    matches = true;
                }
                if (!matches) continue;
            }
            
            filteredUsers.add(user);
        }
        
        updateRecordCount();
    }

    /**
     * تحديث عدد السجلات
     */
    private void updateRecordCount() {
        recordCountLabel.setText(String.format("عدد السجلات: %d من %d", 
            filteredUsers.size(), allUsers.size()));
    }

    /**
     * تحديث حالة القائمة السياقية
     */
    private void updateContextMenuState(User selectedUser) {
        boolean hasSelection = selectedUser != null;
        
        editMenuItem.setDisable(!hasSelection);
        deleteMenuItem.setDisable(!hasSelection);
        resetPasswordMenuItem.setDisable(!hasSelection);
        
        if (hasSelection) {
            lockUserMenuItem.setDisable(selectedUser.isLocked());
            unlockUserMenuItem.setDisable(!selectedUser.isLocked());
        } else {
            lockUserMenuItem.setDisable(true);
            unlockUserMenuItem.setDisable(true);
        }
    }

    // معالجات الأحداث
    @FXML private void handleSearch() { applyFilters(); }
    @FXML private void handleRoleFilter() { applyFilters(); }
    @FXML private void handleStatusFilter() { applyFilters(); }
    @FXML private void handleRefresh() { loadData(); }

    @FXML
    private void handleAddUser() {
        // سيتم تنفيذها لاحقاً
        showUnderDevelopment("إضافة مستخدم جديد");
    }

    @FXML
    private void handleEditUser() {
        User selectedUser = usersTable.getSelectionModel().getSelectedItem();
        if (selectedUser != null) {
            handleEditUser(selectedUser);
        }
    }

    private void handleEditUser(User user) {
        // سيتم تنفيذها لاحقاً
        showUnderDevelopment("تعديل المستخدم: " + user.getFullName());
    }

    @FXML
    private void handleDeleteUser() {
        User selectedUser = usersTable.getSelectionModel().getSelectedItem();
        if (selectedUser != null) {
            handleDeleteUser(selectedUser);
        }
    }

    private void handleDeleteUser(User user) {
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle("تأكيد الحذف");
        confirmDialog.setHeaderText(null);
        confirmDialog.setContentText("هل تريد حذف المستخدم: " + user.getFullName() + "؟");
        
        confirmDialog.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                try {
                    userService.delete(user);
                    loadData();
                    showSuccess("تم حذف المستخدم بنجاح");
                } catch (Exception e) {
                    logger.error("خطأ في حذف المستخدم", e);
                    showError("خطأ في حذف المستخدم: " + e.getMessage());
                }
            }
        });
    }

    @FXML
    private void handleResetPassword() {
        User selectedUser = usersTable.getSelectionModel().getSelectedItem();
        if (selectedUser != null) {
            Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
            confirmDialog.setTitle("تأكيد إعادة تعيين كلمة المرور");
            confirmDialog.setHeaderText(null);
            confirmDialog.setContentText("هل تريد إعادة تعيين كلمة مرور المستخدم: " + selectedUser.getFullName() + "؟");
            
            confirmDialog.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    try {
                        String newPassword = userService.resetPassword(selectedUser.getId());
                        
                        Alert infoDialog = new Alert(Alert.AlertType.INFORMATION);
                        infoDialog.setTitle("كلمة المرور الجديدة");
                        infoDialog.setHeaderText(null);
                        infoDialog.setContentText("كلمة المرور الجديدة: " + newPassword);
                        infoDialog.showAndWait();
                        
                        showSuccess("تم إعادة تعيين كلمة المرور بنجاح");
                    } catch (Exception e) {
                        logger.error("خطأ في إعادة تعيين كلمة المرور", e);
                        showError("خطأ في إعادة تعيين كلمة المرور: " + e.getMessage());
                    }
                }
            });
        }
    }

    @FXML
    private void handleLockUser() {
        User selectedUser = usersTable.getSelectionModel().getSelectedItem();
        if (selectedUser != null) {
            try {
                userService.lockUser(selectedUser.getId(), 60); // قفل لمدة ساعة
                loadData();
                showSuccess("تم قفل المستخدم بنجاح");
            } catch (Exception e) {
                logger.error("خطأ في قفل المستخدم", e);
                showError("خطأ في قفل المستخدم: " + e.getMessage());
            }
        }
    }

    @FXML
    private void handleUnlockUser() {
        User selectedUser = usersTable.getSelectionModel().getSelectedItem();
        if (selectedUser != null) {
            try {
                userService.unlockUser(selectedUser.getId());
                loadData();
                showSuccess("تم إلغاء قفل المستخدم بنجاح");
            } catch (Exception e) {
                logger.error("خطأ في إلغاء قفل المستخدم", e);
                showError("خطأ في إلغاء قفل المستخدم: " + e.getMessage());
            }
        }
    }

    /**
     * عرض رسالة "قيد التطوير"
     */
    private void showUnderDevelopment(String feature) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("قيد التطوير");
        alert.setHeaderText(null);
        alert.setContentText("الميزة '" + feature + "' قيد التطوير");
        alert.showAndWait();
    }

    /**
     * عرض رسالة نجاح
     */
    private void showSuccess(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("نجح");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * عرض رسالة خطأ
     */
    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
