import javax.swing.*;
import java.awt.*;

public class TestApp {
    public static void main(String[] args) {
        // إنشاء النافذة
        JFrame frame = new JFrame("اختبار التطبيق");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(600, 400);
        frame.setLocationRelativeTo(null);
        
        // إنشاء المحتوى
        JPanel panel = new JPanel();
        panel.setBackground(Color.WHITE);
        panel.setLayout(new BorderLayout());
        
        JLabel label = new JLabel("التطبيق يعمل بنجاح!", JLabel.CENTER);
        label.setFont(new Font("Arial", Font.BOLD, 24));
        label.setForeground(Color.BLUE);
        
        JButton button = new JButton("اضغط هنا");
        button.setFont(new Font("Arial", Font.BOLD, 16));
        button.addActionListener(e -> {
            JOptionPane.showMessageDialog(frame, "تم الضغط على الزر بنجاح!");
        });
        
        panel.add(label, BorderLayout.CENTER);
        panel.add(button, BorderLayout.SOUTH);
        
        frame.add(panel);
        frame.setVisible(true);
        
        // رسالة تأكيد
        JOptionPane.showMessageDialog(frame, "التطبيق يعمل بنجاح!");
    }
}
