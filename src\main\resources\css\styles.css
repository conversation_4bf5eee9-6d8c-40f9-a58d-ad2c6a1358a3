/* ملف التصميم الرئيسي لنظام إدارة الشحنات */

.root {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 12px;
    -fx-background-color: #f8f9fa;
}

/* تصميم القوائم */
.menu-bar {
    -fx-background-color: #2c3e50;
    -fx-border-color: #34495e;
    -fx-border-width: 0 0 1 0;
}

.menu-bar .menu {
    -fx-text-fill: white;
}

.menu-bar .menu:hover {
    -fx-background-color: #34495e;
}

.menu-item {
    -fx-text-fill: #2c3e50;
}

.menu-item:focused {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

/* تصميم الأزرار */
.button {
    -fx-background-radius: 5;
    -fx-border-radius: 5;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.button:hover {
    -fx-opacity: 0.8;
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.button:pressed {
    -fx-scale-x: 0.95;
    -fx-scale-y: 0.95;
}

/* تصميم التسميات */
.label {
    -fx-text-fill: #2c3e50;
}

/* تصميم الجداول */
.table-view {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 5;
}

.table-view .column-header {
    -fx-background-color: #ecf0f1;
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.table-view .table-row-cell {
    -fx-background-color: white;
}

.table-view .table-row-cell:odd {
    -fx-background-color: #f8f9fa;
}

.table-view .table-row-cell:selected {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

/* تصميم حقول الإدخال */
.text-field {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 3;
    -fx-padding: 5;
}

.text-field:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2;
}

/* تصميم القوائم المنسدلة */
.combo-box {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 3;
}

.combo-box:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2;
}

/* تصميم التبويبات */
.tab-pane {
    -fx-background-color: white;
}

.tab-pane .tab-header-area {
    -fx-background-color: #ecf0f1;
}

.tab-pane .tab {
    -fx-background-color: #bdc3c7;
    -fx-text-fill: #2c3e50;
    -fx-background-radius: 5 5 0 0;
}

.tab-pane .tab:selected {
    -fx-background-color: white;
    -fx-text-fill: #3498db;
}

/* تصميم النوافذ المنبثقة */
.dialog-pane {
    -fx-background-color: white;
}

.dialog-pane .header-panel {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

/* تصميم شريط التقدم */
.progress-bar {
    -fx-background-color: #ecf0f1;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 3;
}

.progress-bar .bar {
    -fx-background-color: #3498db;
    -fx-background-radius: 2;
}

/* تصميم الرسوم البيانية */
.chart {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 5;
}

.chart-title {
    -fx-text-fill: #2c3e50;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
}

/* تصميم البطاقات */
.card {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-padding: 15;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
}

/* تصميم الأيقونات */
.icon {
    -fx-font-family: "FontAwesome";
    -fx-font-size: 16px;
}

/* تصميم الحالات */
.status-active {
    -fx-text-fill: #27ae60;
    -fx-font-weight: bold;
}

.status-pending {
    -fx-text-fill: #f39c12;
    -fx-font-weight: bold;
}

.status-inactive {
    -fx-text-fill: #e74c3c;
    -fx-font-weight: bold;
}

/* تصميم الرسائل */
.success-message {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-border-color: #c3e6cb;
    -fx-border-width: 1;
    -fx-border-radius: 3;
    -fx-padding: 10;
}

.error-message {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
    -fx-border-color: #f5c6cb;
    -fx-border-width: 1;
    -fx-border-radius: 3;
    -fx-padding: 10;
}

.warning-message {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
    -fx-border-color: #ffeaa7;
    -fx-border-width: 1;
    -fx-border-radius: 3;
    -fx-padding: 10;
}

/* تصميم الشبكة */
.grid-pane {
    -fx-hgap: 10;
    -fx-vgap: 10;
    -fx-padding: 15;
}

/* تصميم الفواصل */
.separator {
    -fx-background-color: #bdc3c7;
}

/* تصميم شريط الأدوات */
.tool-bar {
    -fx-background-color: #ecf0f1;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 5;
}

/* تصميم متجاوب */
@media (max-width: 800px) {
    .root {
        -fx-font-size: 10px;
    }
    
    .button {
        -fx-font-size: 10px;
        -fx-padding: 3;
    }
}
