package com.shipment.erp.service;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.shipment.erp.model.Role;
import com.shipment.erp.repository.RoleRepository;

/**
 * خدمة إدارة الأدوار
 */
@Service
public class RoleService {
    
    @Autowired
    private RoleRepository roleRepository;
    
    /**
     * الحصول على جميع الأدوار
     */
    public List<Role> getAllRoles() {
        return roleRepository.findAll();
    }
    
    /**
     * الحصول على دور بالمعرف
     */
    public Optional<Role> getRoleById(Long id) {
        return roleRepository.findById(id);
    }
    
    /**
     * الحصول على دور بالاسم
     */
    public Optional<Role> getRoleByName(String name) {
        return roleRepository.findByName(name);
    }
    
    /**
     * حفظ دور جديد أو تحديث موجود
     */
    public Role saveRole(Role role) {
        return roleRepository.save(role);
    }
    
    /**
     * حذف دور
     */
    public void deleteRole(Long id) {
        roleRepository.deleteById(id);
    }
    
    /**
     * التحقق من وجود دور بالاسم
     */
    public boolean existsByName(String name) {
        return roleRepository.existsByName(name);
    }
    
    /**
     * الحصول على الأدوار النشطة
     */
    public List<Role> getActiveRoles() {
        return roleRepository.findByActiveTrue();
    }

    /**
     * البحث عن الأدوار النشطة (اسم بديل)
     */
    public List<Role> findActive() {
        return getActiveRoles();
    }
}
